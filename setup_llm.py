#!/usr/bin/env python3
"""
Setup script for LLM Agent Configuration
"""

import os
import sys
import subprocess
import platform

def setup_llm_agent():
    """Interactive setup for LLM agent configuration."""
    
    print("🤖 LLM Agent Setup for Research Paper Classification")
    print("=" * 50)
    
    # Check if API key is already set
    current_key = os.getenv('GROQ_API_KEY')
    if current_key:
        print(f"✅ GROQ_API_KEY is already set (length: {len(current_key)})")
        print("Testing current configuration...")
        
        # Test the current setup
        try:
            from test_llm_agent import test_llm_agent
            if test_llm_agent():
                print("🎉 LLM agent is working correctly!")
                return True
            else:
                print("❌ Current API key is not working. Please get a new one.")
        except ImportError:
            print("⚠️  Could not test current setup")
    
    print("\n📋 Setup Instructions:")
    print("1. Visit https://console.groq.com/")
    print("2. Sign up for a free account")
    print("3. Go to API Keys section")
    print("4. Create a new API key")
    print("5. Copy the API key (starts with 'gsk_')")
    
    # Get API key from user
    api_key = input("\n🔑 Enter your Groq API key: ").strip()
    
    if not api_key:
        print("❌ No API key provided. Setup cancelled.")
        return False
    
    if not api_key.startswith('gsk_'):
        print("⚠️  Warning: API key should start with 'gsk_'. Please verify your key.")
        continue_setup = input("Continue anyway? (y/n): ").lower()
        if continue_setup != 'y':
            return False
    
    # Set environment variable based on OS
    system = platform.system().lower()
    
    if system == "windows":
        print("\n🪟 Setting up for Windows...")
        
        # PowerShell command
        ps_cmd = f'$env:GROQ_API_KEY="{api_key}"'
        print(f"PowerShell command: {ps_cmd}")
        
        # Command Prompt command
        cmd_cmd = f'set GROQ_API_KEY={api_key}'
        print(f"Command Prompt command: {cmd_cmd}")
        
        print("\n📝 To make this permanent, add to your system environment variables:")
        print("1. Open System Properties > Environment Variables")
        print("2. Add new variable: GROQ_API_KEY")
        print("3. Set value to your API key")
        
    elif system == "linux" or system == "darwin":
        print("\n🐧 Setting up for Linux/Mac...")
        
        # Bash command
        bash_cmd = f'export GROQ_API_KEY="{api_key}"'
        print(f"Bash command: {bash_cmd}")
        
        # Add to shell profile
        shell_profile = os.path.expanduser("~/.bashrc")
        if not os.path.exists(shell_profile):
            shell_profile = os.path.expanduser("~/.zshrc")
        
        if os.path.exists(shell_profile):
            add_to_profile = input(f"\nAdd to {shell_profile} for permanent setup? (y/n): ").lower()
            if add_to_profile == 'y':
                try:
                    with open(shell_profile, 'a') as f:
                        f.write(f'\n# Groq API Key for Research Paper Classification\nexport GROQ_API_KEY="{api_key}"\n')
                    print(f"✅ Added to {shell_profile}")
                    print("Please restart your terminal or run: source ~/.bashrc")
                except Exception as e:
                    print(f"❌ Failed to write to {shell_profile}: {e}")
    
    # Test the setup
    print("\n🧪 Testing the setup...")
    os.environ['GROQ_API_KEY'] = api_key
    
    try:
        from test_llm_agent import test_llm_agent
        if test_llm_agent():
            print("🎉 Setup successful! LLM agent is working.")
            return True
        else:
            print("❌ Setup failed. Please check your API key.")
            return False
    except Exception as e:
        print(f"❌ Error testing setup: {e}")
        return False

def main():
    """Main setup function."""
    try:
        success = setup_llm_agent()
        if success:
            print("\n✅ Setup completed successfully!")
            print("You can now run the enhanced application:")
            print("python src/enhanced_app.py")
        else:
            print("\n❌ Setup failed. Please try again.")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\nSetup cancelled by user.")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 