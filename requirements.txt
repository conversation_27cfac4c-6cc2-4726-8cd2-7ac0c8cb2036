# Enhanced Research Paper Classification Requirements

# Core Flask dependencies
Flask==2.3.3
Flask-CORS==4.0.0
Werkzeug==2.3.7

# Machine Learning and Data Processing
pandas==2.1.1
numpy==1.24.3
scikit-learn==1.3.0
joblib==1.3.2

# PDF Processing
PyPDF2==3.0.1
pdfplumber==0.9.0
PyMuPDF==1.23.5
pytesseract==0.3.10
Pillow==10.0.1

# Natural Language Processing
nltk==3.8.1
textstat==0.7.3
gensim==4.3.2

# LLM Integration
groq==0.30.0

# Testing
pytest==7.4.2
pytest-cov==4.1.0

# Development and Deployment
python-dotenv==1.0.0
gunicorn==21.2.0

# Optional: For enhanced functionality
requests==2.31.0
beautifulsoup4==4.12.2

