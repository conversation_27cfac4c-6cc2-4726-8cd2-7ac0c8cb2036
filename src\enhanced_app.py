"""
Enhanced Flask Application with Groq LLM Agent Integration

This enhanced version of the research paper classification system integrates
a Groq LLM agent to provide detailed feedback on publishability, improvement
suggestions, and journal recommendations.
"""

from flask import Flask, request, jsonify, render_template, send_from_directory
from flask_cors import CORS
import os
import uuid
import json
import logging
from werkzeug.utils import secure_filename
import threading
import time
import pandas as pd
import numpy as np
import pickle
import joblib
import PyPDF2
import pdfplumber
import fitz  # PyMuPDF
from PIL import Image
import pytesseract
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.feature_extraction.text import TfidfVectorizer
import re

# Import the LLM agent
from llm_agent import GroqLLMAgent, analyze_paper_with_llm, get_improvement_suggestions, compare_journal_suitability

# Import existing modules
from app import (
    extract_text_combined, parse_sections, generate_metadata_embedded,
    extract_advanced_features, safe_load_model, recommend_journal_self_contained,
    download_nltk_resources, calculate_word_count, calculate_readability,
    calculate_topic_diversity, calculate_sentiment, calculate_keyword_density,
    calculate_section_balance
)

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler("enhanced_app.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("enhanced_app")

# Initialize Flask app
app = Flask(__name__, static_folder="../frontend/static", template_folder="../frontend/templates")
CORS(app)

# Get base directory for reliable path resolution
BASE_DIR = os.path.dirname(os.path.abspath(__file__))

# Configuration
app.config["UPLOAD_FOLDER"] = os.path.join(BASE_DIR, "..", "data", "raw", "papers")
app.config["RESULTS_FOLDER"] = os.path.join(BASE_DIR, "..", "results")
app.config["MODELS_FOLDER"] = os.path.join(BASE_DIR, "..", "models")
app.config["PROCESSED_FOLDER"] = os.path.join(BASE_DIR, "..", "data", "processed")
app.config["TASK1_PREDICTION_FOLDER"] = os.path.join(BASE_DIR, "..", "data", "task1_prediction")
app.config["ALLOWED_EXTENSIONS"] = {"pdf"}
app.config["MAX_CONTENT_LENGTH"] = 16 * 1024 * 1024  # 16MB max upload size

# Create necessary directories
os.makedirs(app.config["UPLOAD_FOLDER"], exist_ok=True)
os.makedirs(app.config["RESULTS_FOLDER"], exist_ok=True)
os.makedirs(app.config["MODELS_FOLDER"], exist_ok=True)
os.makedirs(app.config["PROCESSED_FOLDER"], exist_ok=True)
os.makedirs(app.config["TASK1_PREDICTION_FOLDER"], exist_ok=True)

# Store processing status
processing_status = {}

# Initialize vectorizer globally (trained on reference abstracts)
vectorizer = None
published_abstracts = ""
unpublished_abstracts = ""
published_word_count_mean = 0
unpublished_word_count_mean = 0

# Initialize LLM agent
llm_agent = None

def initialize_llm_agent():
    """Initialize the Groq LLM agent."""
    global llm_agent
    try:
        groq_api_key = os.getenv('GROQ_API_KEY')
        if groq_api_key:
            llm_agent = GroqLLMAgent(api_key=groq_api_key)
            logger.info("Groq LLM agent initialized successfully")
            return True
        else:
            logger.warning("GROQ_API_KEY not found in environment variables. LLM features will be disabled.")
            return False
    except Exception as e:
        logger.error(f"Failed to initialize LLM agent: {str(e)}")
        return False

def allowed_file(filename):
    """Check if the file has an allowed extension."""
    return "." in filename and filename.rsplit(".", 1)[1].lower() in app.config["ALLOWED_EXTENSIONS"]

def compute_similarity(text_1, text_2):
    """Compute text similarity using TF-IDF vectors."""
    global vectorizer
    if not vectorizer or not text_1 or not text_2:
        return 0
    try:
        vec_1 = vectorizer.transform([text_1]).toarray()
        vec_2 = vectorizer.transform([text_2]).toarray()
        return cosine_similarity(vec_1, vec_2)[0][0]
    except Exception:
        return 0

def process_paper_enhanced(file_path, submission_id):
    """Enhanced paper processing with LLM agent integration."""
    try:
        # Update status to processing
        processing_status[submission_id] = {
            "status": "processing",
            "progress": 5,
            "message": "Starting enhanced processing..."
        }
        save_status(submission_id)
        
        # Extract paper ID from filename
        paper_id = os.path.splitext(os.path.basename(file_path))[0]
        
        # Step 1: Extract text from PDF
        processing_status[submission_id] = {
            "status": "processing",
            "progress": 15,
            "message": "Extracting text from PDF..."
        }
        save_status(submission_id)
        
        def progress_callback(message):
            processing_status[submission_id]["message"] = message
            save_status(submission_id)
            
        raw_text = extract_text_combined(file_path, progress_callback)
        if not raw_text.strip():
            raise Exception("Failed to extract text from PDF")
        
        # Parse sections
        sections = parse_sections(raw_text)
        
        # Create a DataFrame with the extracted text
        extracted_df = pd.DataFrame({
            "Paper ID": [paper_id],
            "abstract": [sections.get("abstract", "")],
            "introduction": [sections.get("introduction", "")],
            "methodology": [sections.get("methodology", "")],
            "results": [sections.get("results", "")],
            "conclusion": [sections.get("conclusion", "")]
        })
        
        # Step 2: Generate metadata
        processing_status[submission_id] = {
            "status": "processing",
            "progress": 30,
            "message": "Generating metadata..."
        }
        save_status(submission_id)
        
        paper_data_with_metadata = generate_metadata_embedded(extracted_df, progress_callback=progress_callback)
        
        # Load reference data and fit vectorizer
        reference_path = os.path.join(app.config["PROCESSED_FOLDER"], "metadata_reference.csv")
        reference_df = pd.read_csv(reference_path)

        global vectorizer, published_abstracts, unpublished_abstracts
        global published_word_count_mean, unpublished_word_count_mean
        
        vectorizer = TfidfVectorizer(stop_words="english")
        vectorizer.fit(reference_df["abstract"].fillna(""))

        published_abstracts = " ".join(reference_df[reference_df["Label"] == 1]["abstract"].fillna(""))
        unpublished_abstracts = " ".join(reference_df[reference_df["Label"] == 0]["abstract"].fillna(""))

        published_word_count_mean = reference_df[reference_df["Label"] == 1]["abstract_word_count"].mean()
        unpublished_word_count_mean = reference_df[reference_df["Label"] == 0]["abstract_word_count"].mean()

        # Apply advanced features
        advanced_features = paper_data_with_metadata.apply(extract_advanced_features, axis=1)
        paper_data = pd.concat([paper_data_with_metadata, advanced_features], axis=1)
        
        # Step 3: Run basic classification
        processing_status[submission_id] = {
            "status": "processing",
            "progress": 45,
            "message": "Running basic classification..."
        }
        save_status(submission_id)
        
        # Load model safely
        model, error_msg = safe_load_model(app.config["MODELS_FOLDER"])
        if not model:
            raise Exception(f"Could not load any classification model. Last error: {error_msg}")
        
        # Prepare features
        expected_features = getattr(model, "feature_names_in_", None)
        if expected_features is not None:
            feature_cols = [col for col in expected_features if col in paper_data.columns]
        else:
            feature_cols = [col for col in paper_data.columns 
                            if col not in ["Paper ID", "abstract", "introduction", "methodology", "results", "conclusion"]]
        
        if not feature_cols:
            raise Exception("No valid feature columns identified for classification.")
        
        X = paper_data[feature_cols].fillna(0)
        
        # Make prediction
        try:
            y_prob = model.predict_proba(X)
            confidence = y_prob[0][1]
        except AttributeError:
            y_pred_temp = model.predict(X)
            confidence = float(y_pred_temp[0])
        
        y_pred = model.predict(X)
        publishable = bool(y_pred[0])
        
        # Step 4: LLM Analysis (if available)
        llm_analysis = None
        if llm_agent:
            processing_status[submission_id] = {
                "status": "processing",
                "progress": 60,
                "message": "Performing LLM analysis..."
            }
            save_status(submission_id)
            
            try:
                # Prepare paper content for LLM
                paper_content = f"""
Title: {paper_id}

Abstract:
{sections.get('abstract', '')}

Introduction:
{sections.get('introduction', '')}

Methodology:
{sections.get('methodology', '')}

Results:
{sections.get('results', '')}

Conclusion:
{sections.get('conclusion', '')}
"""
                
                metadata = {
                    "paper_id": paper_id,
                    "basic_classification": {
                        "publishable": publishable,
                        "confidence": confidence
                    }
                }
                
                llm_analysis = llm_agent.analyze_paper(paper_content, metadata)
                logger.info("LLM analysis completed successfully")
                
            except Exception as e:
                logger.error(f"LLM analysis failed: {str(e)}")
                llm_analysis = None
        else:
            logger.info("LLM agent not available - skipping LLM analysis")
            processing_status[submission_id] = {
                "status": "processing",
                "progress": 60,
                "message": "LLM analysis not available - using basic classification only..."
            }
            save_status(submission_id)
        
        # Step 5: Journal recommendation
        processing_status[submission_id] = {
            "status": "processing",
            "progress": 80,
            "message": "Generating journal recommendations..."
        }
        save_status(submission_id)
        
        conference = "N/A"
        rationale = "N/A"
        
        if publishable:
            reference_metadata_path = os.path.join(app.config["PROCESSED_FOLDER"], "metadata_reference.csv")
            conference, rationale = recommend_journal_self_contained(paper_data, reference_metadata_path)
        
        # Step 6: Compile results
        processing_status[submission_id] = {
            "status": "processing",
            "progress": 95,
            "message": "Compiling results..."
        }
        save_status(submission_id)
        
        # Create enhanced result object
        result = {
            "paper_id": paper_id,
            "basic_classification": {
                "publishable": publishable,
                "confidence": float(confidence),
                "conference": conference,
                "rationale": rationale
            },
            "llm_analysis": None,
            "sections": sections
        }
        
        # Add LLM analysis if available
        if llm_analysis:
            result["llm_analysis"] = {
                "publishability_score": llm_analysis.publishability_score,
                "publishability_status": llm_analysis.publishability_status,
                "publishability_reasons": llm_analysis.publishability_reasons,
                "strengths": llm_analysis.strengths,
                "weaknesses": llm_analysis.weaknesses,
                "novelty_assessment": llm_analysis.novelty_assessment,
                "technical_quality": llm_analysis.technical_quality,
                "clarity_assessment": llm_analysis.clarity_assessment,
                "improvement_suggestions": [
                    {
                        "category": suggestion.category,
                        "priority": suggestion.priority,
                        "description": suggestion.description,
                        "specific_actions": suggestion.specific_actions,
                        "impact": suggestion.impact
                    }
                    for suggestion in llm_analysis.improvement_suggestions
                ],
                "journal_recommendations": [
                    {
                        "journal_name": rec.journal_name,
                        "conference_type": rec.conference_type,
                        "suitability_score": rec.suitability_score,
                        "reasons": rec.reasons,
                        "requirements": rec.requirements,
                        "typical_acceptance_criteria": rec.typical_acceptance_criteria,
                        "submission_tips": rec.submission_tips
                    }
                    for rec in llm_analysis.journal_recommendations
                ],
                "comparative_analysis": llm_analysis.comparative_analysis
            }
        
        # Save result
        result_path = os.path.join(app.config["RESULTS_FOLDER"], f"{submission_id}_result.json")
        with open(result_path, "w") as f:
            json.dump(result, f, indent=2)
        
        # Update status
        processing_status[submission_id] = {
            "status": "completed",
            "progress": 100,
            "message": "Enhanced processing completed successfully",
            "result": result
        }
        save_status(submission_id)
        
        return True
        
    except Exception as e:
        logger.exception(f"Error in enhanced processing {submission_id}: {str(e)}")
        processing_status[submission_id] = {
            "status": "error",
            "progress": 100,
            "message": f"Error: {str(e)}"
        }
        save_status(submission_id)
        return False

def save_status(submission_id):
    """Save processing status to file."""
    status_path = os.path.join(app.config["RESULTS_FOLDER"], f"{submission_id}_status.json")
    try:
        if submission_id in processing_status:
            with open(status_path, "w") as f:
                json.dump(processing_status[submission_id], f)
    except Exception as e:
        logger.error(f"Failed to save status for {submission_id}: {str(e)}")

# Flask Routes
@app.route("/")
def index():
    """Render the main page."""
    return render_template("index.html")

@app.route("/upload", methods=["POST"])
def upload_file():
    """Handle file upload with enhanced processing."""
    if "file" not in request.files:
        return jsonify({"error": "No file part"}), 400
    file = request.files["file"]
    if file.filename == "":
        return jsonify({"error": "No selected file"}), 400
    if not allowed_file(file.filename):
        return jsonify({"error": "File type not allowed"}), 400
    
    try:
        submission_id = str(uuid.uuid4())
        filename = secure_filename(file.filename)
        file_path = os.path.join(app.config["UPLOAD_FOLDER"], filename)
        
        # Ensure filename is unique
        base, ext = os.path.splitext(filename)
        counter = 1
        while os.path.exists(file_path):
            file_path = os.path.join(app.config["UPLOAD_FOLDER"], f"{base}_{counter}{ext}")
            counter += 1
            
        file.save(file_path)
        logger.info(f"File saved to: {file_path}")
        
        processing_status[submission_id] = {
            "status": "uploaded",
            "progress": 0,
            "message": "File uploaded, waiting for enhanced processing"
        }
        save_status(submission_id)
        
        # Start enhanced processing
        thread = threading.Thread(target=process_paper_enhanced, args=(file_path, submission_id))
        thread.daemon = True
        thread.start()
        
        return jsonify({
            "submission_id": submission_id,
            "message": "File uploaded successfully, enhanced processing started"
        })
    except Exception as e:
        logger.exception(f"Error uploading file: {str(e)}")
        return jsonify({"error": "Internal server error during upload."}), 500

@app.route("/status/<submission_id>", methods=["GET"])
def get_status(submission_id):
    """Get processing status."""
    status_path = os.path.join(app.config["RESULTS_FOLDER"], f"{submission_id}_status.json")
    if os.path.exists(status_path):
        try:
            with open(status_path, "r") as f:
                status = json.load(f)
            return jsonify(status)
        except Exception as e:
            logger.error(f"Failed to load status file for {submission_id}: {str(e)}")
    
    if submission_id in processing_status:
        return jsonify(processing_status[submission_id])
    
    return jsonify({"error": "Submission ID not found"}), 404

@app.route("/result/<submission_id>", methods=["GET"])
def get_result(submission_id):
    """Get processing result."""
    result_path = os.path.join(app.config["RESULTS_FOLDER"], f"{submission_id}_result.json")
    if os.path.exists(result_path):
        try:
            with open(result_path, "r") as f:
                result = json.load(f)
            return jsonify(result)
        except Exception as e:
            logger.error(f"Failed to load result file for {submission_id}: {str(e)}")

    if submission_id in processing_status and "result" in processing_status[submission_id]:
        return jsonify(processing_status[submission_id]["result"])
    
    return jsonify({"error": "Result not found or processing incomplete/failed"}), 404

# New LLM-specific endpoints
@app.route("/api/analyze-paper", methods=["POST"])
def analyze_paper_endpoint():
    """Comprehensive paper analysis using LLM."""
    if not llm_agent:
        return jsonify({"error": "LLM agent not available"}), 503
    
    try:
        data = request.get_json()
        paper_content = data.get("paper_content", "")
        metadata = data.get("metadata", {})
        
        if not paper_content:
            return jsonify({"error": "Paper content is required"}), 400
        
        analysis = llm_agent.analyze_paper(paper_content, metadata)
        
        # Convert to dictionary for JSON response
        result = {
            "publishability_score": analysis.publishability_score,
            "publishability_status": analysis.publishability_status,
            "publishability_reasons": analysis.publishability_reasons,
            "strengths": analysis.strengths,
            "weaknesses": analysis.weaknesses,
            "novelty_assessment": analysis.novelty_assessment,
            "technical_quality": analysis.technical_quality,
            "clarity_assessment": analysis.clarity_assessment,
            "improvement_suggestions": [
                {
                    "category": suggestion.category,
                    "priority": suggestion.priority,
                    "description": suggestion.description,
                    "specific_actions": suggestion.specific_actions,
                    "impact": suggestion.impact
                }
                for suggestion in analysis.improvement_suggestions
            ],
            "journal_recommendations": [
                {
                    "journal_name": rec.journal_name,
                    "conference_type": rec.conference_type,
                    "suitability_score": rec.suitability_score,
                    "reasons": rec.reasons,
                    "requirements": rec.requirements,
                    "typical_acceptance_criteria": rec.typical_acceptance_criteria,
                    "submission_tips": rec.submission_tips
                }
                for rec in analysis.journal_recommendations
            ],
            "comparative_analysis": analysis.comparative_analysis
        }
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Error in paper analysis endpoint: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route("/api/get-improvement-suggestions", methods=["POST"])
def get_improvement_suggestions_endpoint():
    """Generate specific improvement recommendations."""
    if not llm_agent:
        return jsonify({"error": "LLM agent not available"}), 503
    
    try:
        data = request.get_json()
        paper_content = data.get("paper_content", "")
        
        if not paper_content:
            return jsonify({"error": "Paper content is required"}), 400
        
        suggestions = llm_agent.suggest_improvements(paper_content)
        
        result = [
            {
                "category": suggestion.category,
                "priority": suggestion.priority,
                "description": suggestion.description,
                "specific_actions": suggestion.specific_actions,
                "impact": suggestion.impact
            }
            for suggestion in suggestions
        ]
        
        return jsonify({"suggestions": result})
        
    except Exception as e:
        logger.error(f"Error in improvement suggestions endpoint: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route("/api/compare-journals", methods=["POST"])
def compare_journals_endpoint():
    """Compare suitability across multiple journals."""
    if not llm_agent:
        return jsonify({"error": "LLM agent not available"}), 503
    
    try:
        data = request.get_json()
        paper_content = data.get("paper_content", "")
        target_journals = data.get("target_journals", [])
        
        if not paper_content:
            return jsonify({"error": "Paper content is required"}), 400
        if not target_journals:
            return jsonify({"error": "Target journals list is required"}), 400
        
        recommendations = llm_agent.compare_journals(paper_content, target_journals)
        
        result = [
            {
                "journal_name": rec.journal_name,
                "conference_type": rec.conference_type,
                "suitability_score": rec.suitability_score,
                "reasons": rec.reasons,
                "requirements": rec.requirements,
                "typical_acceptance_criteria": rec.typical_acceptance_criteria,
                "submission_tips": rec.submission_tips
            }
            for rec in recommendations
        ]
        
        return jsonify({"recommendations": result})
        
    except Exception as e:
        logger.error(f"Error in journal comparison endpoint: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route("/api/refine-analysis", methods=["POST"])
def refine_analysis_endpoint():
    """Refine analysis based on user feedback."""
    if not llm_agent:
        return jsonify({"error": "LLM agent not available"}), 503
    
    try:
        data = request.get_json()
        paper_content = data.get("paper_content", "")
        previous_analysis = data.get("previous_analysis", {})
        user_query = data.get("user_query", "")
        
        if not paper_content or not user_query:
            return jsonify({"error": "Paper content and user query are required"}), 400
        
        # Convert previous_analysis dict back to PaperAnalysis object if needed
        # For simplicity, we'll pass it as is to the refinement function
        refined_response = llm_agent.refine_analysis(paper_content, None, user_query)
        
        return jsonify({"refined_analysis": refined_response})
        
    except Exception as e:
        logger.error(f"Error in analysis refinement endpoint: {str(e)}")
        return jsonify({"error": str(e)}), 500

# Existing routes
@app.route("/visualizations")
def get_visualizations():
    """Get all global visualizations."""
    visualizations = []
    vis_files = [
        "confusion_matrix.png",
        "feature_importance.png",
        "roc_curve_comparison.png",
        "model_performance_comparison.png",
        "shap_summary.png",
        "conference_distribution.png"
    ]
    for vis_file in vis_files:
        vis_path = os.path.join(app.config["RESULTS_FOLDER"], vis_file)
        if os.path.exists(vis_path):
            visualizations.append({
                "name": vis_file.split(".")[0].replace("_", " ").title(),
                "path": f"/visualization/{vis_file}"
            })
    return jsonify(visualizations)

@app.route("/visualization/<filename>")
def serve_visualization(filename):
    """Serve visualization file."""
    safe_filename = secure_filename(filename)
    return send_from_directory(app.config["RESULTS_FOLDER"], safe_filename)

@app.route("/about")
def about():
    """Render the about page with visualizations."""
    visualizations = []
    vis_files = [
        "confusion_matrix.png",
        "feature_importance.png",
        "roc_curve_comparison.png",
        "model_performance_comparison.png",
        "shap_summary.png",
        "conference_distribution.png"
    ]
    for vis_file in vis_files:
        vis_path = os.path.join(app.config["RESULTS_FOLDER"], vis_file)
        if os.path.exists(vis_path):
            visualizations.append({
                "name": vis_file.split(".")[0].replace("_", " ").title(),
                "path": f"/visualization/{vis_file}"
            })
    return render_template("about.html", visualizations=visualizations)

@app.route("/documentation")
def documentation():
    """Render the documentation page."""
    return render_template("documentation.html")

if __name__ == "__main__":
    # Initialize NLTK resources
    download_nltk_resources()
    
    # Initialize LLM agent
    initialize_llm_agent()
    
    port = int(os.environ.get("PORT", 5000))
    app.run(host="0.0.0.0", port=port, debug=False)

