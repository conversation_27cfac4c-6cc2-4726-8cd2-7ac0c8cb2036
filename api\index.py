"""
Vercel API entry point for the Enhanced Research Paper Classification system.

This file serves as the main entry point for Vercel deployment, importing
and configuring the enhanced Flask application.
"""

import os
import sys

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

# Import the enhanced Flask application
from enhanced_app import app

# Configure for Vercel deployment
app.config['ENV'] = 'production'
app.config['DEBUG'] = False

# Ensure CORS is properly configured for production
from flask_cors import CORS
CORS(app, origins=['*'])

# Export the app for Vercel
application = app

if __name__ == "__main__":
    app.run()

