"""
LLM Agent for Research Paper Analysis using Groq API

This module provides comprehensive analysis of research papers using Groq's LLM capabilities.
It generates detailed feedback on publishability, improvement suggestions, and journal recommendations.
"""

import os
import json
import logging
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
from groq import Groq
import re

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ImprovementSuggestion:
    """Represents a specific improvement suggestion for a research paper."""
    category: str  # methodology, writing, experiments, related_work, etc.
    priority: str  # high, medium, low
    description: str
    specific_actions: List[str]
    impact: str  # Expected impact of implementing this suggestion

@dataclass
class JournalRecommendation:
    """Represents a journal recommendation with detailed analysis."""
    journal_name: str
    conference_type: str  # conference, journal, workshop
    suitability_score: float  # 0-10 scale
    reasons: List[str]
    requirements: List[str]
    typical_acceptance_criteria: str
    submission_tips: List[str]

@dataclass
class PaperAnalysis:
    """Comprehensive analysis of a research paper."""
    publishability_score: float  # 0-10 scale
    publishability_status: str  # publishable, needs_improvement, not_publishable
    publishability_reasons: List[str]
    improvement_suggestions: List[ImprovementSuggestion]
    journal_recommendations: List[JournalRecommendation]
    comparative_analysis: str
    strengths: List[str]
    weaknesses: List[str]
    novelty_assessment: str
    technical_quality: str
    clarity_assessment: str

class GroqLLMAgent:
    """
    LLM Agent for analyzing research papers using Groq API.
    
    This agent provides comprehensive analysis including publishability assessment,
    improvement suggestions, and journal recommendations.
    """
    
    def __init__(self, api_key: Optional[str] = None):
        """
        Initialize the Groq LLM Agent.
        
        Args:
            api_key: Groq API key. If None, will try to get from environment variable.
        """
        self.api_key = api_key or os.getenv('GROQ_API_KEY')
        if not self.api_key:
            raise ValueError("Groq API key is required. Set GROQ_API_KEY environment variable or pass api_key parameter.")
        
        self.client = Groq(api_key=self.api_key)
        self.model = "llama-3.1-70b-versatile"  # Using Llama 3.1 70B for better analysis
        
    def analyze_paper(self, paper_content: str, metadata: Dict = None) -> PaperAnalysis:
        """
        Perform comprehensive analysis of a research paper.
        
        Args:
            paper_content: Full text content of the research paper
            metadata: Optional metadata about the paper (title, authors, etc.)
            
        Returns:
            PaperAnalysis object containing detailed analysis
        """
        try:
            logger.info("Starting comprehensive paper analysis")
            
            # Generate comprehensive analysis
            analysis_prompt = self._build_analysis_prompt(paper_content, metadata)
            analysis_response = self._call_groq_api(analysis_prompt)
            
            # Parse the response into structured data
            paper_analysis = self._parse_analysis_response(analysis_response)
            
            logger.info("Paper analysis completed successfully")
            return paper_analysis
            
        except Exception as e:
            logger.error(f"Error in paper analysis: {str(e)}")
            raise
    
    def suggest_improvements(self, paper_content: str, current_analysis: PaperAnalysis = None) -> List[ImprovementSuggestion]:
        """
        Generate specific improvement suggestions for a research paper.
        
        Args:
            paper_content: Full text content of the research paper
            current_analysis: Optional existing analysis to build upon
            
        Returns:
            List of ImprovementSuggestion objects
        """
        try:
            logger.info("Generating improvement suggestions")
            
            improvement_prompt = self._build_improvement_prompt(paper_content, current_analysis)
            improvement_response = self._call_groq_api(improvement_prompt)
            
            suggestions = self._parse_improvement_response(improvement_response)
            
            logger.info(f"Generated {len(suggestions)} improvement suggestions")
            return suggestions
            
        except Exception as e:
            logger.error(f"Error generating improvement suggestions: {str(e)}")
            raise
    
    def compare_journals(self, paper_content: str, target_journals: List[str]) -> List[JournalRecommendation]:
        """
        Compare suitability of a paper for multiple journals/conferences.
        
        Args:
            paper_content: Full text content of the research paper
            target_journals: List of journal/conference names to compare
            
        Returns:
            List of JournalRecommendation objects ranked by suitability
        """
        try:
            logger.info(f"Comparing suitability for {len(target_journals)} journals")
            
            comparison_prompt = self._build_comparison_prompt(paper_content, target_journals)
            comparison_response = self._call_groq_api(comparison_prompt)
            
            recommendations = self._parse_comparison_response(comparison_response)
            
            # Sort by suitability score
            recommendations.sort(key=lambda x: x.suitability_score, reverse=True)
            
            logger.info("Journal comparison completed")
            return recommendations
            
        except Exception as e:
            logger.error(f"Error in journal comparison: {str(e)}")
            raise
    
    def refine_analysis(self, paper_content: str, previous_analysis: PaperAnalysis, user_query: str) -> str:
        """
        Refine analysis based on user questions or additional context.
        
        Args:
            paper_content: Full text content of the research paper
            previous_analysis: Previous analysis results
            user_query: User's specific question or request for clarification
            
        Returns:
            Refined analysis addressing the user's query
        """
        try:
            logger.info("Refining analysis based on user query")
            
            refinement_prompt = self._build_refinement_prompt(paper_content, previous_analysis, user_query)
            refinement_response = self._call_groq_api(refinement_prompt)
            
            logger.info("Analysis refinement completed")
            return refinement_response
            
        except Exception as e:
            logger.error(f"Error in analysis refinement: {str(e)}")
            raise
    
    def _build_analysis_prompt(self, paper_content: str, metadata: Dict = None) -> str:
        """Build comprehensive analysis prompt for Groq API."""
        metadata_str = ""
        if metadata:
            metadata_str = f"Paper Metadata: {json.dumps(metadata, indent=2)}\n\n"
        
        return f"""You are an expert research paper reviewer with extensive experience in academic publishing across multiple domains including computer science, machine learning, artificial intelligence, and related fields. Your task is to provide a comprehensive analysis of the following research paper.

{metadata_str}Paper Content:
{paper_content[:8000]}  # Limit content to avoid token limits

Please provide a detailed analysis in the following JSON format:

{{
    "publishability_score": <float 0-10>,
    "publishability_status": "<publishable|needs_improvement|not_publishable>",
    "publishability_reasons": [
        "<detailed reason 1>",
        "<detailed reason 2>",
        "..."
    ],
    "strengths": [
        "<strength 1>",
        "<strength 2>",
        "..."
    ],
    "weaknesses": [
        "<weakness 1>",
        "<weakness 2>",
        "..."
    ],
    "novelty_assessment": "<detailed assessment of novelty and contribution>",
    "technical_quality": "<assessment of technical rigor and methodology>",
    "clarity_assessment": "<assessment of writing quality and presentation>",
    "improvement_suggestions": [
        {{
            "category": "<methodology|writing|experiments|related_work|evaluation>",
            "priority": "<high|medium|low>",
            "description": "<detailed description>",
            "specific_actions": ["<action 1>", "<action 2>", "..."],
            "impact": "<expected impact of implementing this suggestion>"
        }}
    ],
    "journal_recommendations": [
        {{
            "journal_name": "<journal/conference name>",
            "conference_type": "<conference|journal|workshop>",
            "suitability_score": <float 0-10>,
            "reasons": ["<reason 1>", "<reason 2>", "..."],
            "requirements": ["<requirement 1>", "<requirement 2>", "..."],
            "typical_acceptance_criteria": "<detailed criteria>",
            "submission_tips": ["<tip 1>", "<tip 2>", "..."]
        }}
    ],
    "comparative_analysis": "<detailed comparison of why certain venues are better suited than others>"
}}

Focus on providing actionable, specific feedback that will help improve the paper's quality and chances of acceptance. Consider factors such as:
- Technical contribution and novelty
- Experimental design and evaluation
- Related work coverage
- Writing clarity and organization
- Reproducibility and methodology
- Significance of results

Provide honest, constructive criticism while highlighting the paper's strengths."""

    def _build_improvement_prompt(self, paper_content: str, current_analysis: PaperAnalysis = None) -> str:
        """Build improvement suggestions prompt for Groq API."""
        analysis_context = ""
        if current_analysis:
            analysis_context = f"""
Previous Analysis Context:
- Publishability Score: {current_analysis.publishability_score}/10
- Status: {current_analysis.publishability_status}
- Main Weaknesses: {', '.join(current_analysis.weaknesses[:3])}

"""
        
        return f"""You are an expert research mentor helping to improve a research paper. Based on the paper content and any previous analysis, provide specific, actionable improvement suggestions.

{analysis_context}Paper Content:
{paper_content[:8000]}

Please provide detailed improvement suggestions in the following JSON format:

{{
    "suggestions": [
        {{
            "category": "<methodology|writing|experiments|related_work|evaluation|presentation>",
            "priority": "<high|medium|low>",
            "description": "<detailed description of the issue and why it needs improvement>",
            "specific_actions": [
                "<specific action 1>",
                "<specific action 2>",
                "..."
            ],
            "impact": "<expected impact on paper quality and acceptance chances>"
        }}
    ]
}}

Focus on:
1. High-impact improvements that will significantly enhance the paper
2. Specific, actionable steps the authors can take
3. Prioritization based on effort vs. impact
4. Technical improvements to methodology and experiments
5. Writing and presentation enhancements
6. Addressing potential reviewer concerns

Be specific and constructive in your suggestions."""

    def _build_comparison_prompt(self, paper_content: str, target_journals: List[str]) -> str:
        """Build journal comparison prompt for Groq API."""
        journals_str = ", ".join(target_journals)
        
        return f"""You are an expert in academic publishing with deep knowledge of various journals and conferences. Analyze the following research paper and compare its suitability for the specified venues.

Paper Content:
{paper_content[:8000]}

Target Venues: {journals_str}

Please provide a detailed comparison in the following JSON format:

{{
    "recommendations": [
        {{
            "journal_name": "<exact journal/conference name>",
            "conference_type": "<conference|journal|workshop>",
            "suitability_score": <float 0-10>,
            "reasons": [
                "<specific reason why this venue is suitable/unsuitable>",
                "..."
            ],
            "requirements": [
                "<specific requirement 1>",
                "<specific requirement 2>",
                "..."
            ],
            "typical_acceptance_criteria": "<detailed description of what this venue typically accepts>",
            "submission_tips": [
                "<specific tip for this venue>",
                "..."
            ]
        }}
    ]
}}

For each venue, consider:
- Scope and focus alignment
- Technical depth requirements
- Novelty expectations
- Typical paper length and format
- Review criteria and standards
- Acceptance rates and competitiveness
- Timeline and submission deadlines

Provide honest assessments and specific guidance for each venue."""

    def _build_refinement_prompt(self, paper_content: str, previous_analysis: PaperAnalysis, user_query: str) -> str:
        """Build refinement prompt for addressing specific user queries."""
        return f"""You are an expert research advisor. Based on the previous analysis of a research paper and a specific user query, provide a detailed, focused response.

Previous Analysis Summary:
- Publishability Score: {previous_analysis.publishability_score}/10
- Status: {previous_analysis.publishability_status}
- Main Strengths: {', '.join(previous_analysis.strengths[:3])}
- Main Weaknesses: {', '.join(previous_analysis.weaknesses[:3])}

User Query: {user_query}

Paper Content (excerpt):
{paper_content[:6000]}

Please provide a detailed response that directly addresses the user's query while referencing the paper content and previous analysis. Be specific, actionable, and helpful."""

    def _call_groq_api(self, prompt: str) -> str:
        """Make API call to Groq and return response."""
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert research paper reviewer and academic mentor. Provide detailed, constructive, and actionable feedback."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=0.3,  # Lower temperature for more consistent analysis
                max_tokens=4000,
                top_p=0.9
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            logger.error(f"Groq API call failed: {str(e)}")
            raise
    
    def _parse_analysis_response(self, response: str) -> PaperAnalysis:
        """Parse Groq response into PaperAnalysis object."""
        try:
            # Extract JSON from response
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if not json_match:
                raise ValueError("No JSON found in response")
            
            data = json.loads(json_match.group())
            
            # Parse improvement suggestions
            improvement_suggestions = []
            for suggestion_data in data.get('improvement_suggestions', []):
                suggestion = ImprovementSuggestion(
                    category=suggestion_data.get('category', ''),
                    priority=suggestion_data.get('priority', 'medium'),
                    description=suggestion_data.get('description', ''),
                    specific_actions=suggestion_data.get('specific_actions', []),
                    impact=suggestion_data.get('impact', '')
                )
                improvement_suggestions.append(suggestion)
            
            # Parse journal recommendations
            journal_recommendations = []
            for journal_data in data.get('journal_recommendations', []):
                recommendation = JournalRecommendation(
                    journal_name=journal_data.get('journal_name', ''),
                    conference_type=journal_data.get('conference_type', 'journal'),
                    suitability_score=float(journal_data.get('suitability_score', 0)),
                    reasons=journal_data.get('reasons', []),
                    requirements=journal_data.get('requirements', []),
                    typical_acceptance_criteria=journal_data.get('typical_acceptance_criteria', ''),
                    submission_tips=journal_data.get('submission_tips', [])
                )
                journal_recommendations.append(recommendation)
            
            # Create PaperAnalysis object
            analysis = PaperAnalysis(
                publishability_score=float(data.get('publishability_score', 0)),
                publishability_status=data.get('publishability_status', 'needs_improvement'),
                publishability_reasons=data.get('publishability_reasons', []),
                improvement_suggestions=improvement_suggestions,
                journal_recommendations=journal_recommendations,
                comparative_analysis=data.get('comparative_analysis', ''),
                strengths=data.get('strengths', []),
                weaknesses=data.get('weaknesses', []),
                novelty_assessment=data.get('novelty_assessment', ''),
                technical_quality=data.get('technical_quality', ''),
                clarity_assessment=data.get('clarity_assessment', '')
            )
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error parsing analysis response: {str(e)}")
            # Return default analysis if parsing fails
            return PaperAnalysis(
                publishability_score=5.0,
                publishability_status="needs_improvement",
                publishability_reasons=["Analysis parsing failed"],
                improvement_suggestions=[],
                journal_recommendations=[],
                comparative_analysis="Unable to generate comparative analysis",
                strengths=[],
                weaknesses=["Analysis parsing error"],
                novelty_assessment="Unable to assess novelty",
                technical_quality="Unable to assess technical quality",
                clarity_assessment="Unable to assess clarity"
            )
    
    def _parse_improvement_response(self, response: str) -> List[ImprovementSuggestion]:
        """Parse improvement suggestions from Groq response."""
        try:
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if not json_match:
                return []
            
            data = json.loads(json_match.group())
            suggestions = []
            
            for suggestion_data in data.get('suggestions', []):
                suggestion = ImprovementSuggestion(
                    category=suggestion_data.get('category', ''),
                    priority=suggestion_data.get('priority', 'medium'),
                    description=suggestion_data.get('description', ''),
                    specific_actions=suggestion_data.get('specific_actions', []),
                    impact=suggestion_data.get('impact', '')
                )
                suggestions.append(suggestion)
            
            return suggestions
            
        except Exception as e:
            logger.error(f"Error parsing improvement response: {str(e)}")
            return []
    
    def _parse_comparison_response(self, response: str) -> List[JournalRecommendation]:
        """Parse journal comparison from Groq response."""
        try:
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if not json_match:
                return []
            
            data = json.loads(json_match.group())
            recommendations = []
            
            for rec_data in data.get('recommendations', []):
                recommendation = JournalRecommendation(
                    journal_name=rec_data.get('journal_name', ''),
                    conference_type=rec_data.get('conference_type', 'journal'),
                    suitability_score=float(rec_data.get('suitability_score', 0)),
                    reasons=rec_data.get('reasons', []),
                    requirements=rec_data.get('requirements', []),
                    typical_acceptance_criteria=rec_data.get('typical_acceptance_criteria', ''),
                    submission_tips=rec_data.get('submission_tips', [])
                )
                recommendations.append(recommendation)
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Error parsing comparison response: {str(e)}")
            return []

# Utility functions for integration with existing system
def analyze_paper_with_llm(paper_content: str, metadata: Dict = None, groq_api_key: str = None) -> Dict:
    """
    Convenience function to analyze a paper and return results as dictionary.
    
    Args:
        paper_content: Full text content of the research paper
        metadata: Optional metadata about the paper
        groq_api_key: Optional Groq API key
        
    Returns:
        Dictionary containing analysis results
    """
    try:
        agent = GroqLLMAgent(api_key=groq_api_key)
        analysis = agent.analyze_paper(paper_content, metadata)
        return asdict(analysis)
    except Exception as e:
        logger.error(f"Error in paper analysis: {str(e)}")
        return {
            "error": str(e),
            "publishability_score": 0.0,
            "publishability_status": "error",
            "publishability_reasons": [f"Analysis failed: {str(e)}"]
        }

def get_improvement_suggestions(paper_content: str, groq_api_key: str = None) -> List[Dict]:
    """
    Convenience function to get improvement suggestions.
    
    Args:
        paper_content: Full text content of the research paper
        groq_api_key: Optional Groq API key
        
    Returns:
        List of improvement suggestions as dictionaries
    """
    try:
        agent = GroqLLMAgent(api_key=groq_api_key)
        suggestions = agent.suggest_improvements(paper_content)
        return [asdict(suggestion) for suggestion in suggestions]
    except Exception as e:
        logger.error(f"Error getting improvement suggestions: {str(e)}")
        return []

def compare_journal_suitability(paper_content: str, target_journals: List[str], groq_api_key: str = None) -> List[Dict]:
    """
    Convenience function to compare journal suitability.
    
    Args:
        paper_content: Full text content of the research paper
        target_journals: List of journal/conference names
        groq_api_key: Optional Groq API key
        
    Returns:
        List of journal recommendations as dictionaries
    """
    try:
        agent = GroqLLMAgent(api_key=groq_api_key)
        recommendations = agent.compare_journals(paper_content, target_journals)
        return [asdict(recommendation) for recommendation in recommendations]
    except Exception as e:
        logger.error(f"Error comparing journal suitability: {str(e)}")
        return []

