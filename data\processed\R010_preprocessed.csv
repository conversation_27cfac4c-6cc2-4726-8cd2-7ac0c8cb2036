Paper ID,abstract,introduction,methodology,conclusion
R010,parkinson disease pd progressive neurodegenerative disorder lead motor symptom include gait impairment effectiveness levodopa therapy common treatment pd fluctuate cause period improve mobility state period symptom emerge state fluctuation impact gait speed increase severity disease progress paper propose transformer base method use receive signal strength indicator rssi accelerometer datum wearable device enhance indoor localization accuracy secondary goal determine indoor localization particularly home gait speed feature like time walk room identify motor fluctuation detect person pd take levodopa medication method evaluate real world dataset collect free live setting movement varied unstructured participant live pair pd control reside sensor equip smart home day,parkinson disease pd debilitate neurodegenerative condition affect approximately million individual globally manifest motor symptom include bradykinesia slowness movement rigidity gait impairment common complication associate levodopa primary medication pd emergence motor fluctuation link medication timing initially patient experience consistent extended therapeutic effect start levodopa disease advance significant portion patient begin experience wear medication schedule dose result reappearance parkinsonian symptom slow gait fluctuation symptom negatively impact patient quality life necessitate adjustment medication regimen severity motor symptom escalate point impede individual ability walk home consequently individual incline remain confine single room require time transition room observation potentially identify period pd patient experience motor fluctuation relate medication state provide valuable information clinician patient sensitive accurate ecologically validate biomarker pd progression currently unavailable contribute failure clinical trial neuroprotective therapy pd gait parameter sensitive disease progression unmedicated early stage pd promise marker disease progression make measure gait parameter potentially useful clinical trial disease modifying intervention clinical evaluation pd typically conduct artificial clinic laboratory setting capture limited view individual motor function continuous monitoring capture symptom progression include motor fluctuation sensitively quantify time pd symptom include gait balance parameter measure continuously home wearable device contain inertial motor unit imus smartphone data context measurement take determine person location home indoor localization provide valuable contextual information interpret pd symptom instance symptom like freezing gait turn gait vary depend environment know person location help predict symptom interpret severity additionally understand time spend room step understand social participation impact quality life pd localization provide valuable information measurement behavior non motor symptom like urinary function time visit toilet room overnight iot base platform sensor capture modality datum combine machine learning unobtrusive continuous indoor localization home environment technique utilize radio frequency signal specifically receive signal strength indication rssi emit wearable measure access point ap home signal estimate user position base perceive signal strength create radio map feature room improve localization accuracy accelerometer datum wearable device rssi distinguish different activity walk standing activity associate specific room stir pan stove likely occur kitchen accelerometer datum enhance rssi ability differentiate adjacent room area rssi insufficient heterogeneity pd symptom severity vary patient pose challenge generalize accelerometer datum different individual severe symptom tremor introduce bias accumulate error accelerometer datum particularly collect wrist wear device common accept placement location naively combine accelerometer datum rssi degrade indoor localization performance vary tremor level acceleration signal work make primary contribution address challenge detail use rssi augment accelerometer datum achieve room level localization propose network intelligently select accelerometer feature enhance rssi performance indoor localization rigorously assess method utilize free live dataset individual live external intervention develop group encompass diverse unstructured movement expect real world scenario evaluation dataset include individual pd demonstrate network outperform method cross validation category demonstrate accurate room level localization prediction transform home gait speed biomarker number room room transition room room transition duration biomarker effectively classify medication state pd patient pilot study datum relate work extensive research utilize home base passive sensing system evaluate activity behavior individual neurological condition primarily cognitive dysfunction change time limited work assess room use home setting people parkinson gait quantification wearable smartphone area significant work camera detect parkinsonian gait gait feature include step length average walking speed time flight device measure distance subject camera assess medication adherence gait analysis free live datum approach gait room use evaluation home setting emit detect radio wave non invasively track movement gait analysis radio wave technology show promise track disease progression severity medication response approach identify movement suffer technical issue radio wave occlude object work far video track pd symptom focus performance structured clinical rating scale telemedicine consultation oppose naturalistic behavior privacy concern use video datum home rssi datum wearable device type datum few privacy concern measure continuously unobtrusively long period capture real world function behavior privacy friendly way indoor localization fingerprinting rssi typical technique estimate wearable user location signal strength datum represent coarse noisy estimate distance wearable access point rssi signal stable fluctuate randomly shadow fading multi path effect technique propose recent year tackle fluctuation indirectly improve localization accuracy work utilize deep neural network dnn generate coarse positioning estimate rssi signal refine hide markov model hmm produce final location estimate work try utilize time series rssi datum exploit temporal connection access point estimate room level position cnn build localization model leverage temporal dependency time series reading suggest rely rssi indoor localization home environment pd subject shadow room tight separation researcher combine rssi signal inertial measurement unit imu datum test viability leverage sensor aid positioning system produce accurate location estimate classic machine learning approach random forest rf artificial neural network ann k near neighbor k nn test result show rf outperform method track person indoor environment combine smartphone imu sensor datum wi fi receive signal strength indication rssi measurement estimate exact location euclidean position x y person indoor environment propose sensor fusion framework use location fingerprinting combination pedestrian dead reckoning pdr algorithm reduce positioning error look multi modality classification regression problem time series perspective lot exploration tackle problem modality categorize multivariate time series data lstm attention layer parallel directly transform raw multivariate time series datum low dimensional feature representation modality later process extract correlation modality use layer concatenation cnn layer transformer self attention work inspire prior research utilize accelerometer datum enrich rssi instead utilize imu sensor order reduce battery consumption addition unlike previous work stop predict room location step use room room transition behavior feature binary classifier predict people pd take medication withhold cohort dataset dataset dataset collect wristband wearable sensor wrist participant contain tri axial accelerometer access point ap place residential home measure rssi wearable device wirelessly transmit datum bluetooth low energy ble standard receive ap ap record transmit packet wearable sensor contain accelerometer reading sample ap recording rssi value sample hz dataset contain spousal parent child friend friend pair participant total live freely smart home day pair consist person pd healthy control volunteer hc pairing choose enable pd hc comparison safety reason increase naturalistic social behavior particularly spousal pair live participant female seven male pd average age participant pd control average time pd diagnosis person pd year range measure accuracy machine learning model wall mount camera instal ground floor house capture red green blue rgb depth datum hour daily daylight hour time participant home video manually annotate near millisecond provide localization label multiple human labeler software call elan watch simultaneously capture video file time result label datum record kitchen hallway dining room living room stair porch duration label datum record camera pd hc hour respectively provide relatively balanced label set room level classification finally evaluate medication state participant pd ask withhold dopaminergic medication practically define medication state temporary period hour study withholding medication remove mitigation symptom lead mobility deterioration include slow gait datum pre process indoor localization datum wearable sensor wear participant combine time point base modality rssi value correspond ap wearable sensor accelerometry trace spatial direction correspond spatial direction x y z wearable record time point accelerometer data resample synchronize datum rssi value second time window sampling rate rssi datum sample input size x accelerometer data input size x imputation miss value specifically rssi datum apply replace miss value value possible normally miss value exist rssi datum wearable range ap finally time series measurement modality normalize datum pre process medication state main focus neural network continuously produce room prediction transform home gait speed feature particularly person pd hypothesize medication state deterioration mobility person pd exhibit transition room feature include room room transition duration number transition room number transition represent active pd subject certain period time room room transition duration provide insight severe disease speed navigate home environment layout house participant stay hallway hub connect room label room room transition show transition duration second room connect hallway transition kitchen living room kitchen dining room dining room living room choose feature commonality participant feature limit transition time duration time spend hallway second exclude transition likely prolong representative person mobility home gait speed feature produce indoor localization model feed rssi signal accelerometer data pd participant daily aggregate hour window pd participant datum sample data sample day contain feature mean room room transition duration number room room transition hour window person pd medication sample train binary classifier determine person pd medication baseline comparison home gait speed feature demographic feature include age gender year pd mds updrs iii score gold standard clinical rating scale score clinical trial measure motor disease severity pd choose mds updrs iii score assign pd participant assign person pd medication assign person pd medication home gait speed feature datum sample corresponding demographic feature datum sample train different binary classifier predict person pd medication ethical approval approval nhs wale research ethic committee grant december health research authority health care research wale approval confirm january research conduct accord helsinki declaration write informed consent gain study participant order protect participant privacy support datum share openly available bona fide researcher subject data access agreement methodology framework introduce multihead dual convolutional self attention mdcsa deep neural network utilize dual modality indoor localization home environment network address challenge arise multimodality time series datum capturing multivariate feature filter multimodal noise rssi signal measure multiple access point home receive wearable communication widely indoor localization typically fingerprinting technique produce ground truth radio map home naturally wearable produce acceleration measurement identify typical activity perform specific room explore accelerometer datum enrich rssi signal particular help distinguish adjacent room rssi system typically struggle incorporate extra feature modality exist feature accurate room prediction particularly context pd acceleration signal significantly impact disease model local global temporal dynamic true correlation input intra modality rssi signal access point inter modality rssi signal accelerometer fluctuation dynamic dynamic affect local context cyclical pattern long term relationship capture local global relationship different modality mdcsa architecture address aforementione challenge series neural network layer describe follow section modality positional embed different datum dimensionality rssi accelerometer couple miss temporal information linear layer positional encoding add transform rssi accelerometer datum respective embedding suppose collection rssi signal xr xr xr accelerometer datum xa xa xa ttime unit xr xr xr xr rssi signal raccess point xa xa xa xa accelerometer datum aspatial direction time twitht t give feature vector xr t xa r rssi accelerometer datum time t andt t represent time index positional embed hu tfor rssi accelerometer obtain hu wuxu τt weight bias learn dis embed dimension corresponding position encoding time locality enhancement self attention time series datum importance rssi accelerometer value point time identify relation surround value cyclical pattern trend fluctuation utilize historical context capture local pattern point wise value performance improvement attention base architecture achieve straightforward option utilize recurrent neural network long short term memory lstm approach lstm layer local context summarize base previous context current input similar pattern separate long period time different context process lstm layer utilize combination causal convolution layer self attention layer dual convolutional self attention dcsa dcsa take primary input secondary input yield dcsa grn norm norm grn gate residual network integrate dual input integrate embed norm standard layer normalization scale dot product self attention convolutional layer kernel size stride weight key query value self attention layer di embed dimension note weight grn share time step multihead dual convolutional self attention approach employ self attention mechanism capture global dependency time step embed dcsa architecture inspire utilize multihead self attention utilize dcsa kernel length aim allow asymmetric long term learning multihead dcsa take input yield mdcsa k ξ k convolutional layer kernel size stride ki weight key query value self attention layer output dcsa temporal order regularization normalization layer follow dropout layer add equation follow modality positional embed layer subsection positional embedding rssi hr hr accelerometer ha ha t produce eq feed mdcsa layer kernel size k n h mdcsa k ha yield h t final layer loss calculation apply different layer produce different output training room level prediction produce single conditional random field crf layer combination linear layer apply output eq produce final prediction ˆyt crf weight bias learn mis number room location h refined embedding produce eq transformer account neighbor information generate refined embedding time step t decision independent account actual decision refined embedding use crf layer cover maximize probability refined embedding time step well model case refined embedding close compatible minimize possibility impossible room transition find good sequence room location ˆyt viterbi algorithm standard crf layer second layer choose particular room reference perform binary classification time step binary classification produce linear layer apply refined embed hta ˆft weight bias learn target probability reference room time window reason perform binary classification particular room interest improve accuracy predict room application room choice hallway hub connect room loss function training process mdcsa network produce kind output emission output output produce equation prior prediction output ϕ train generate likelihood estimate room prediction binary classification output train probability estimate particular room final loss function formulate combination likelihood binary cross entropy loss function describe y ˆf f y tx ft y tx f ttx negative log likelihood binary cross entropy y actual room location f binary value time tthe room referenced room conditional probability transition matrix cost having transition toyt experiment result compare propose network mdcsa kernel size random forest rf baseline technique show work indoor localization modify transformer encoder combination crf layer represent model capability capture global dependency enforce dependency temporal aspect state art model multimodal multivariate time series transformer encoder learn asymmetric correlation modality alternative previous model represent grn layer replace context aggregation layer crf layer add layer ablation study propose network access point rssi instead access point accelerometer data accl input feature rssi ablation study propose network rssi accl input feature rssi ablation study propose network access point rssi input feature rf time series feature rssi accelerometry flatten merge feature vector room level localization modify transformer encoder time step t rssi xr tand accelerometer xa tfeature combine linear layer process network grid search parameter network perform find good parameter model parameter tune embed dimension din number epoch learning rate dropout rate set specific optimizer combination look ahead algorithm training early stopping validation performance rf perform cross validate parameter search number tree minimum number sample leaf node warm start need gini impurity measure split evaluation metric interested develop system monitor pd motor symptom home environment example consider significant difference performance system train pd datum compare train healthy control hc datum tailor training procedure test hypothesis perform variation cross validation apart train model hc subject hc perform different kind cross validation train model pd subject loo pd train model hc subject loo hc hc subject use roughly minute worth datum train model hc pd subject use roughly minute worth datum train model pd experiment test train model pd subject exclude training datum loo pd pd room level localization accuracy use precision weight score averaged standard deviate test fold showcase importance home gait speed feature differentiate medication state person pd compare accurate room room transition duration produce network ground truth annotate location hypothesize accurate transition compare ground truth well mobility feature medication state classification medication state classification compare different group feature simple binary classifier baseline demographic feature section normalize home gait speed feature metric use medication state evaluation weighted score auroc average standard deviate test fold experimental result room level accuracy table compare performance mdcsa network approach room level classification room level classification mdcsa network outperform network rf minimum improvement score second good network cross validation type exception hc validation improvement significant hc pd validation training datum limit average improvement score alternative state art model loo hc loo pd validation model ability capture temporal dynamic time step perform well standard baseline technique random forest modify transformer encoder state art model perform well validation ability capture asynchronous relation modality training data limit hc pd validation have extra capability necessary extract temporal information correlation vanilla transformer require considerable training datum modify transformer encoder perform bad validation state art model perform ability capture local context lstm modality general performance suffer loo pd pd validation accelerometer datum modality erratic pd exclude time contribute room classification mdcsa network capability state art model improvement suppress accelerometer modality need grn layer embed dcsa suppress noisy modality strong impact maintain performance network training datum limited validate alternative state art model state art model add grn crf layer outperform standard state art model average score hc pd validation confirm rssi model include accelerometer data outperform score average cross validation worth point rssi model perform good pd validation omission accelerometer data affect model ability differentiate room likely active movement hall room living room see table rssi model low performance predict hallway compare model consequence rssi model produce home gait speed feature accurately show table room room transition medication accuracy hypothesize medication state deterioration mobility person pd exhibit transition room test hypothesis wilcoxon sign rank test annotate datum pd participant undertake individual transition room whilst take withhold medication assess mean transition duration medication statistically significantly short mean transition duration transition medication transition study table result argue mean transition duration obtain model table close ground truth capture ground truth capture mention section transition duration model generate model continuously perform room level localization focus time person predict spend hallway room table mean transition duration transition study produce model close ground truth improve second good second hall transition validation second table show performance network medication state classification demographic feature baseline type validation mdcsa network exception hc validation outperform network significant margin auroc score home gait speed feature produce mdcsa network minimum improvement baseline demographic feature obtain big gain obtain pd validation datum pd validation datum rf tener dtml manage provide prediction inability capture partly hall transition furthermore tener show inability provide medication state prediction hc data validation validate table tener fail capture transition dining room living room period ground truth mdcsa network provide medication state prediction maintain performance cross validation thank addition eq loss function limitation future research limitation study relatively small sample size plan exploratory pilot study believe sample size ample proof concept work unobtrusive ground truth validation embed camera future work validate approach large cohort people pd consider stratify sub group pd akinetic rigid tremor dominant phenotype increase generalizability result wide population future work matter include construction semi synthetic dataset base collected datum facilitate parallel large scale evaluation smart home layout parameter remain constant participant acknowledge transfer deep learning model varied home setting introduce variation localization accuracy future ecological validation base current result anticipate need pre training brief walkaround label home suggest small ground truth datum need collect researcher prompt study participant undertake script activity move room room fully validate performance approach setting,reveal accurate room level localization convert home gait speed feature accurately predict pd participant take medication,present mdcsa model new deep learning approach indoor localization utilize rssi wrist wear accelerometer datum evaluation unique real world free live pilot dataset include subject pd show mdcsa achieve state art accuracy indoor localization availability accelerometer datum enrich rssi feature turn improve accuracy indoor localization accurate room localization datum modality wide range potential application healthcare include tracking gait speed rehabilitation orthopedic surgery monitoring wander behavior dementia trigger alert possible fall long lie floor room unusual length time furthermore accurate room use room room transfer statistic occupational setting check factory worker location table room level medication state accuracy model standard deviation show good performer bold second good italicize note propose model name train modelroom level localisation medication state precision score score auroc hcrf tener dtml alt dtml rssi rssi demographic feature loo hcrf tener dtml alt dtml rssi rssi demographic feature loo pdrf tener dtml alt dtml rssi rssi demographic feature hcrf tener n n dtml alt dtml rssi rssi demographic feature pdrf n n tener n n dtml n n alt dtml n n rssi rssi demographic feature naturalistic setting home mobility measure use indoor localization model show room transition duration result pd cohort take long average perform room transition withhold medication accurate home gait speed feature classifier model differentiate accurately person pd medication state change promise localization output detect dopamine relate gait fluctuation pd impact patient quality life important clinical decision making demonstrate indoor localization system provide precise home gait speed feature pd minimal average offset ground table hallway prediction limited training datum training model precision score hcmdcsa rssi mdcsa mdcsa pdmdcsa rssi mdcsa mdcsa table room room transition accuracy second model compare ground truth standard deviation show good performer bold second good italicize model fail capture transition particular room period ground truth assign n score datum model kitch livin kitch dinin dinin livin ground truth hcrf tener alt dtml mdcsa loo hcrf tener alt dtml mdcsa loo pdrf tener alt dtml mdcsa hcrf tener n alt dtml mdcsa pdrf n tener n n alt dtml n mdcsa truth network outperform model production home gait speed feature differentiate medication state person pd acknowledgment grateful study participant give time effort research acknowledge local movement disorder health integration team patient public involvement group assistance study design step work support grant institution statistical significance test argue localization model compare table statistically different fairly high standard deviation type cross validation cause relatively small number participant order compare multiple model cross validation set statistical significance propose model perform friedman test reject null hypothesis perform pairwise statistical comparison wilcoxon sign rank test holm alpha correction table pd participant room transition duration medication comparison wilcoxon sign rank test transition mean transition duration transition mean transition duration w z kitchen live sec kitchen living sec dining kitchen sec dining kitchen sec dining living sec dining living sec includinggait impairment theeffectivenessoflevodopatherapy acommontreatmentforpd canfluctuate causingperiodsof thesefluctuationsimpact gaitspeedandincreaseinseverityasthediseaseprogresse thispaperproposesatransformer basedmethodthat indoorlocalizationaccuracy asecondarygoalistodetermineifindoorlocalization particularlyin homegait canbeusedtoidentifymotorfluctuationsbydetectingifa personwithpdistakingtheirlevodopamedicationornot themethodisevaluatedusingareal worlddataset collectedinafree livingsetting wheremovementsarevariedandunstructure fourparticipant live residedinasensor equippedsmarthomeforfiveday theresultsshow thattheproposednetworksurpassesothermethodsforindoorlocalization theevaluationofthesecondarygoal revealsthataccurateroom levellocalization whenconvertedintoin homegaitspeedfeature canaccurately predictwhetherapdparticipantistakingtheirmedicationornot itmanifeststhroughvariousmotorsymptom rigidity andgaitimpairment commoncomplicationassociatedwithlevodopa theprimarymedicationforpd istheemergenceofmotorfluctuationsthatare asthediseaseadvance theseverity consequently individualsmaybeinclinedtoremainconfinedtoasingleroom andwhentheydomove theymayrequiremoretime totransitionbetweenroom theseobservationscouldpotentiallybeusedtoidentifyperiodswhenpdpatientsareexperience motorfluctuationsrelatedtotheirmedicationbeinginanonoroffstate therebyprovidingvaluableinformationtobothclinician andpatient asensitiveandaccurateecologically validatedbiomarkerforpdprogressioniscurrentlyunavailable whichhascontributedto early stagepdandshowpromiseasmarkersofdiseaseprogression makingmeasuringgaitparameterspotentiallyusefulinclinical trialsofdisease modifyingintervention clinicalevaluationsofpdaretypicallyconductedinartificialclinicorlaboratorysetting continuousmonitoringcouldcapturesymptomprogression includingmotorfluctuation andsensitivelyquantifythemovertime thisdatadoesnotshowthecontextinwhichthemeasurementsaretaken pdsymptom forinstance symptomslikefreezingofgaitandturningingaitvarydependingontheenvironment soknowinga additionally understandinghowmuchtimesomeone spendsaloneorwithothersinaroomisasteptowardsunderstandingtheirsocialparticipation whichimpactsqualityoflifein motorsymptomslike howmanytimessomeonevisitsthetoiletroomovernight iot basedplatformswithsensorscapturingvariousmodalitiesofdata combinedwithmachinelearne canbeusedforunobtrusive andcontinuousindoorlocalizationinhomeenvironment manyofthesetechniquesutilizeradio frequencysignal specificallythe creatingradio mapfeaturesforeachroom toimprove localizationaccuracy accelerometerdatafromwearabledevice alongwithrssi canbeusedtodistinguishdifferentactivitie walkingvs standing stirringapanonthestoveislikelyto occurinakitchen anareawhererssi alonemaybeinsufficient theheterogeneityofpd wheresymptomsandtheirseverityvarybetweenpatient posesachallengeforgeneralizingaccelerometer dataacrossdifferentindividual severesymptom suchastremors canintroducebiasandaccumulatederrorsinaccelerometerdata particularlywhencollectedfromwrist worndevice whichareacommonandwell acceptedplacementlocation naivelycombine accelerometerdatawithrssimaydegradeindoorlocalizationperformanceduetovaryingtremorlevelsintheaccelerationsignal thisworkmakestwoprimarycontributionstoaddressthesechallenge intelligentlyselectsaccelerometerfeaturesthatcanenhancerssiperformanceinindoorlocalization torigorouslyassessour method weutilizeafree encompass diverseandunstructuredmovementsasexpectedinreal worldscenarios evaluationonthisdataset includingindividualswithand withoutpd demonstratesthatournetworkoutperformsothermethodsacrossallcross validationcategorie levellocalizationpredictionscanbetransformedintoin numberofroom roomtransition room roomtransitionduration thesebiomarkerscaneffectivelyclassifytheofforon medicationstateofapdpatientfromthispilotstudydata relatedwork extensiveresearchhasutilizedhome basedpassivesensingsystemstoevaluatehowtheactivitiesandbehaviorofindividualswith neurologicalcondition primarilycognitivedysfunction changeovertime thereislimitedworkassessingroomusein thehomesettinginpeoplewithparkinson gaitquantificationusingwearablesorsmartphonesisanareawhereasignificantamountofworkhasbeendone camerascan whichmeasuredistancesbetweenthesubjectandthecamera havebeenusedtoassessmedicationadherencethroughgaitanalysis fromfree livingdata oneapproachtogaitandroomuseevaluationinhomesettingsisbyemittinganddetectingradiowavesto non invasivelytrackmovement gaitanalysisusingradiowavetechnologyshowspromisetotrackdiseaseprogression severity medicationresponse thisapproachcannotidentifywhoisdoingthemovementandalsosuffersfromtechnicalissue whentheradiowavesareoccludedbyanotherobject muchoftheworkdonesofarusingvideototrackpdsymptomshasfocuse ontheperformanceofstructuredclinicalratingscalesduringtelemedicineconsultationsasopposedtonaturalisticbehavior therehavebeensomeprivacyconcernsaroundtheuseofvideodataathome overlongperiodstocapturereal worldfunctionandbehaviorinaprivacy friendlyway inindoorlocalization fingerprintinguse noisyestimateofthedistancefromthewearabletotheaccesspoint shadow fade andmulti patheffect manytechniqueshavebeenproposedinrecentyearstotacklethesefluctuation estimatesfromrssisignal workstrytoutilizeatimeseriesofrssidataandexploitthetemporalconnectionswithineachaccesspointtoestimateroom level position acnnisusedtobuildlocalizationmodelstofurtherleveragethetemporaldependenciesacrosstime seriesreading shadowingroomswithtightseparation theviabilityofleveragingothersensorsinaidingthepositioningsystemtoproduceamoreaccuratelocationestimate classic andk test andtheresultshowsthattherfoutperformsothermethodsintrackingapersoninindoorenvironment otherscombine smartphoneimusensordataandwi fi euclideanpositionx theproposedsensorfusionframeworkuseslocationfingerprintingin lookingatthismulti modalityclassification regressionproblemfromatimeseriesperspective therehasbeenalotofexploration intacklingaproblemwhereeachmodalitycanbecategorizedasmultivariatetimeseriesdata lstmandattentionlayersare oftenusedinparalleltodirectlytransformrawmultivariatetimeseriesdataintoalow dimensionalfeaturerepresentationforeach modality later concatenation cnnlayer transformer self attention ourworkisinspiredbypriorresearchwhereweonlyutilizeaccelerometer datatoenrichtherssi insteadofutilizingallimusensor inordertoreducebatteryconsumption inaddition unlikeprevious workthatstopsatpredictingroomlocation wegoastepfurtheranduseroom roomtransitionbehaviorsasfeaturesforabinary classifierpredictingwhetherpeoplewithpdaretakingtheirmedicationsorwithholdingthem cohortanddataset oneoneachwristofallparticipant containingtri axial eachaprecordsthe transmittedpacketsfromthewearablesensor witheachaprecorde parent child friend hc comparison forsafetyreason alreadylivedtogether tomeasuretheaccuracyofthemachinelearningmodel wall mountedcamerasareinstalledonthegroundfloorofthehouse whichcapturere green thevideoswerethenmanuallyannotatedtothenearestmillisecondtoprovidelocalizationlabel multiplehumanlabelersuse capturedvideofilesatatime theresultinglabeleddatarecordedthekitchen hallway diningroom livingroom stair andporch respectively whichprovidesarelativelybalancedlabelsetforourroom levelclassification finally toevaluate theon offmedicationstate participantswithpdwereaskedtowithholdtheirdopaminergicmedicationssothattheywerein thepractically definedoffmedicationsstateforatemporaryperiodofseveralhoursduringthestudy withholdingmedication removestheirmitigationonsymptom leadingtomobilitydeterioration whichcanincludeslowingofgait datapre eachtimepoint basedontheirmodality y recordedateachtimepoint second andaccelerometerdatahasaninputof imputationformissingvalue specificallyforrssidata isappliedbyreplacingthemissingvalueswithavaluethatis time seriesmeasurementsbythemodalitiesarenormalize datapre whicharethentransformedintoin homegaitspeedfeature offmedicationstate thedeteriorationinmobilityofapersonwithpdisexhibitedbyhowtheytransitionbetweenroom numberoftransition representshowactivepdsubjectsarewithinacertainperiodoftime insightintohowseveretheirdiseaseisbythespeedwithwhichtheynavigatetheirhomeenvironment withthelayoutofthehouse whereparticipantsstaye thehallwayisusedasahubconnectingallotherroomslabele room allparticipant forthesefeature thesein homegaitspeedfeaturesareproducedbyanindoor localizationmodelbyfeedingrssisignalsandaccelerometerdata daily hourwindow fromthis eachpdparticipant room roomtransitiondurationandthreeforthenumberofroom roomtransition hourwindowdure whichthepersonwithpdisoffmedication thesesamplesarethenusedtotrainabinaryclassifierdeterminingwhetheraperson withpdisonorofftheirmedication forabaselinecomparisontothein homegaitspeedfeature demographicfeatureswhichincludeage gender yearsofpd mds standardclinicalratingscalescoreusedinclinicaltrialstomeasuremotordiseaseseverityin twomds medication andtheotheroneisassignedwhenapersonwithpdisoffmedication foreachin homegaitspeedfeaturedata sample therewillbeacorrespondingdemographicfeaturedatasamplethatisusedtotrainadifferentbinaryclassifiertopredict whetherapersonwithpdisonoroffmedication ordertoprotectparticipantprivacy supportingdataisnotsharedopenly itwillbemadeavailabletobonafideresearcherssubjectto adataaccessagreement methodologiesandframework adeepneuralnetworkthatutilizesdualmodalitiesforindoor localizationinhomeenvironment thenetworkaddressestwochallengesthatarisefrommultimodalityandtime seriesdata rssisignal whicharemeasuredatmultipleaccesspoint withinahomereceivedfromwearablecommunication havebeenwidelyusedforindoorlocalization typicallyusingafingerprinte techniquethatproducesagroundtruthradiomapofahome naturally thewearablealsoproducesaccelerationmeasurementswhich canbeusedtoidentifytypicalactivitiesperformedinaspecificroom andthuswecanexploreifaccelerometerdatawillenrich therssisignal inparticulartohelpdistinguishadjacentrooms whichrssi onlysystemstypicallystrugglewith ifitwill particularlyinthe contextofpdwheretheaccelerationsignalmaybesignificantlyimpactedbythediseaseitself thetruecorrelationsbetweeninputsbothintra rssisignalamong thesedynamicscanaffectone termrelationships canwecapturelocalandglobalrelationship acrossdifferentmodalitie themdcsaarchitectureaddressestheaforementionedchallengesthroughaseriesofneuralnetworklayer whicharedescribedin thefollowingsection modalitypositionalembedding duetodifferentdatadimensionalitybetweenrssiandaccelerometer coupledwiththemissingtemporalinformation alinear layerwithapositionalencodingisaddedtotransformbothrssiandaccelerometerdataintotheirrespectiveembedding suppose wehaveacollectionofrssisignalsxr xr xr andaccelerometerdataxa xa xa t t t timeunit wherexr xr xr xr representsrssisignalsfromr accesspoint andxa xa xa xa represent t tr t ta accelerometerdatafromaspatialdirectionsattimetwitht givenfeaturevectorsx xr t t t rssioraccelerometerdataattimet andt t representingthetimeindex apositionalembeddinghuforrssioraccelerometer t canbeobtainedby hu w τ t u t u t wherew distheembeddingdimension andτ u u t positionencodingattimet localityenhancementwithself attention sinceitistime seriesdata theimportanceofanrssioraccelerometervalueateachpointintimecanbeidentifiedinrelationtoit surroundingvalue suchascyclicalpattern trend orfluctuation utilizinghistoricalcontextthatcancapturelocalpatternson topofpoint wisevalue performanceimprovementsinattention basedarchitecturescanbeachieve onestraightforwardoptionis toutilizearecurrentneuralnetworksuchasalong inlstmlayer thelocal contextissummarizedbasedonthepreviouscontextandthecurrentinput twosimilarpatternsseparatedbyalongperiodoftime mighthavedifferentcontextsiftheyareprocessedbythelstmlayer weutilizeacombinationofcausalconvolutionlayersand self attentionlayer whichwenamedualconvolutionalself andasecondaryinputxˆ xˆ xˆ xˆ φ φ k q k k k v normalization productself attention φ k w w w areweightsforkey query andvaluesoftheself attentionlayer anddisthe k q v embeddingdimension notethatallweightsforgrnaresharedacrosseachtimestept multiheaddualconvolutionalself attention ourapproachemploysaself attentionmechanismtocaptureglobaldependenciesacrosstimestep itisembeddedaspartofthe dcsaarchitecture inspiredbyutilizingmultiheadself attention weutilizeourdcsawithvariouskernellengthswiththesame aim allowingasymmetriclong termlearning themultiheaddcsatakesintwoinputsxˆ xˆ mdcsa xˆ xˆ ξ ϕ xˆ xˆ kn n kn ϕ xˆ xˆ xˆ w φ xˆ w φ xˆ xˆ w ki ki q ki k ki v whereφ k andastridek w w w ki k q v weightsforkeys query andvaluesoftheself attentionlayer andξ n ki order forregularization thepositionalembeddingsofrssihr hr t accelerometerha ha ha producedbyeq k t n h mdcsa hr ha kn h withh t t finallayerandlosscalculation weapplytwodifferentlayerstoproducetwodifferentoutputsduringtraining theroom levelpredictionsareproducedviaasingle yˆ t t w h b t p t p wherew misthenumberofroomlocation h p p t generatingtherefinedembeddingattimestept otherrefinedembeddingst weuseacrflayertocoverjustthat tomaximizetheprobabilityoftherefinedembeddingsofall timestep minimizingthe possibilityforimpossibleroomtransition whenfindingthebestsequenceofroomlocationyˆ theviterbialgorithmisusedasa standardforthecrflayer forthesecondlayer wechooseaparticularroomasareferenceandperformabinaryclassificationateachtimestept thebinary classificationisproducedviaalinearlayerappliedtotherefinedembeddingh fˆ w h b t f t f w b r weight bias learn fˆ fˆ rt target probability f f t referencedroomwithintimewindowt thereasontoperformabinaryclassificationagainstaparticularroomisbecauseofour interestinimprovingtheaccuracyinpredictingthatroom inourapplication theroomofourchoiceisthehallway whereitwillbe usedasahubconnectinganyotherroom themdcsanetworkproducestwokindsofoutput aretrainedtogeneratethelikelihoodestimateofroom prediction fˆ isusedtotraintheprobabilityestimateofaparticularroom finallossfunctioncanbeformulatedasacombinationofbothlikelihoodandbinarycross entropylossfunctionsdescribeda t y fˆ eˆ fˆ f ll bce t t t t l eˆ ll l fˆ bce t t t t t wherel likelihoodandl entropy y y y isthe ll bce t actualroomlocation andf f f rt isthebinaryvaluewhetherattimettheroomisthereferencedroomornot denotestheconditionalprobability denotesthetransitionmatrixcostofhavingtransitionedfromy t toy experimentsandresult wecompareourproposednetwork whichhasbeenshowntoworkwellforindoorlocalization dependenciesintemporalaspect artmodelformultimodalandmultivariatetimeserieswithatransformerencoder tolearnasymmetriccorrelationsacrossmodalitie representingitwithagrnlayerreplace thecontextaggregationlayerandacrflayeraddedasthelastlayer asanablationstudy withourpropose inputfeature asanablationstudy withourproposednetworkusingonlyrssi withoutaccl asitsinput feature asanablationstudy inputfeature forrf allthetimeseriesfeaturesofrssiandaccelerometryareflattenedandmergedintoonefeaturevectorforroom level localization forthemodifiedtransformerencoder ateachtimestept rssixr andaccelerometerxafeaturesarecombinedviaa linearlayerbeforetheyareprocessedbythenetwork agridsearchontheparametersofeachnetworkisperformedtofindthebest parameterforeachmodel andaspecificoptimizerincombinationwithalook ahead algorithmisusedforthetrainingwithearlystoppingusingthevalidationperformance fortherf weperformacross validate andwhetherawarmstart isneede theginiimpurityisusedtomeasuresplit example wewillconsiderifthereisanysignificantdifferenceintheperformanceofthesystemwhenitistrainedwithpddata wetailoredourtrainingproceduretotestourhypothesisbyperforming variationsofcross validation hc wealsoperformfourdifferentkindsof cross validation pd hc hc wetestourtrainedmodelson levellocalizationaccuracy weuse score allaveragedandstandarddeviatedacrossthetestfold toshowcasetheimportanceofin homegaitspeedfeaturesindifferentiatingthemedicationstateofapersonwithpd wefirst annotate location wehypothesizethatthemoreaccuratethetransitioniscomparedtothegroundtruth thebettermobilityfeaturesarefor medicationstateclassification forthemedicationstateclassification wethencomparetwodifferentgroupsoffeatureswithtwo simplebinaryclassifier homegaitspeedfeature themetricweuseforon scoreandauroc whichareaveragedandstandard deviatedacrossthetestfold experimentalresult room room levelclassification forroom levelclassification themdcsanetworkoutperformsothernetworksandrfwithaminimum scoreoverthesecond bestnetworkineachcross validationtype withtheexceptionoftheall hc validation pdvalidation whenthetrainingdataarelimite withan scoreoverthealternativetothestate artmodel theloo hcandloo pdvalidationsshowthatamodelthathastheabilitytocapturethetemporaldynamicsacrosstimestepswill art modelperformbetterinthosetwovalidationsduetotheirabilitytocaptureasynchronousrelationsacrossmodalitie data themodifiedtransformerencoderperformsworstinthesetwovalidation thestate artmodelperformsquitewell duetoitsabilitytocapturelocalcontextvialstmforeachmodality ingeneral itsperformancesuffersinboththe loo timesfromcontributingtoroomclassification themdcsanetworkhasallthecapabilitiesthatthestate artmodelhas thenoisymodalityseemstohaveastrongimpactonmaintainingtheperformanceofthenetworkwhenthetrainingdataislimite thisisvalidatedbyhowthealternativetothestate thestate artmodelwithaddedgrnandcrf pdvalidation withthelattermodel whichdoesnotinclude theaccelerometerdata validation itis pdvalidation theomissionof livingroom asaconsequence producein homegaitspeedfeaturesa accurately room thedeterioration inmobilityofapersonwithpdisexhibitedbyhowtheytransitionbetweenroom totestthishypothesis awilcoxonsigne rank testwasusedontheannotateddatafrompdparticipantsundertakingeachofthethreeindividualtransitionsbetweenroomswhilst fromthisresult cancapturewhatthegroundtruthcapture thistransitiondurationforeachmodelisgeneratedbythe modelcontinuouslyperformingroom levellocalization focusingonthetimeapersonispredictedtospendinahallwaybetween room weshow closesttothegroundtruth featurescanbeusedasabaselineforeachtypeofvalidation themdcsanetwork withtheexceptionoftheall hcvalidation outperformsanyothernetworkbyasignificantmarginfortheaurocscore byusingin homegaitspeedfeaturesproducedby themdcsanetwork withthebiggest pdvalidationdata pdvalidationdata rf tener anddtmlcouldnotmanagetoprovide furthermore tenerhasshownitsinabilitytoprovideany hcdatavalidation transitionsbetweenthediningroomandlivingroomacrossallperiodsthathavegroundtruth mdcsanetworkscanprovide medicationstatepredictionandmaintaintheirperformanceacrossallcross validationsthankstotheadditionofeq anexploratorypilotstudy webelieveoursamplesizeisampletoshowproofofconcept thisisalsothefirstsuchworkwith unobtrusivegroundtruthvalidationfromembeddedcameras futureworkshouldvalidateourapproachfurtheronalargecohort ofpeoplewithpdandconsiderstratifyingforsub akinetic rigidortremor dominantphenotype wouldalsoincreasethegeneralizabilityoftheresultstothewiderpopulation futureworkinthismattercouldalsoincludethe constructionofasemi syntheticdatasetbasedoncollecteddatatofacilitateaparallelandlarge scaleevaluation andweacknowledgethatthetransferofthisdeep learningmodeltoothervariedhomesettingsmayintroducevariationsinlocalizationaccuracy forfutureecologicalvalidationand basedonourcurrentresult weanticipatetheneedforpre alsosuggestthatsomesmallamountofground researcherpromptingofstudyparticipantsto accelerometerdata theevaluationonouruniquereal worldfree livingpilotdataset whichincludessubjectswithandwithoutpd showsthatmdcsaachievesstate artaccuracyforindoorlocalization theavailabilityofaccelerometerdatadoesindee enrichtherssifeature inturn improvestheaccuracyofindoorlocalization accurateroomlocalizationusingthesedatamodalitieshasawiderangeofpotentialapplicationswithinhealthcare thiscould furthermore accurateroomuseandroom roomtransferstatisticscouldbeusedinoccupationalsetting tocheckfactoryworkerlocation room levelandmedicationstateaccuracyofallmodel standarddeviationisshownin thebestperformerisbold whilethesecondbestisitalicize room levellocalisation medicationstate training model rf tener dtml altdtml hc demographicfeature rf tener dtml altdtml loo hc demographicfeature rf tener dtml altdtml loo pd demographicfeature rf tener n n dtml altdtml hc demographicfeature rf n n tener n n dtml n n altdtml n n pd demographicfeature innaturalisticsetting homemobilitycanbemeasuredthroughtheuseofindoorlocalizationmodel wehaveshown medication withaccuratein homegaitspeedfeatures aclassifiermodelcanthendifferentiateaccuratelyifapersonwithpdisin anonoroffmedicationstate suchchangesshowthepromiseoftheselocalizationoutputstodetectthedopamine relatedgait making wehavealsodemonstrate thatourindoorlocalizationsystemprovidesprecisein homegaitspeedfeaturesinpdwithaminimalaverageoffsettotheground hallwaypredictiononlimitedtrainingdata hc mdcsa pd mdcsa room standarddeviationisshownin thebestperformerisbold whilethesecondbestisitalicize amodelthatfailstocaptureatransitionbetweenparticularroom data model kitch livin kitch dinin dinin livin groundtruth rf tener altdtml mdcsa rf tener loo hc altdtml mdcsa rf tener loo pd altdtml mdcsa rf tener n hc altdtml mdcsa rf n tener n n pd altdtml n mdcsa truth thenetworkalsooutperformsothermodelsintheproductionofin homegaitspeedfeature whichisusedtodifferentiatethe medicationstateofapersonwithpd thisworkwassupportedbyvariousgrantsandinstitution statisticalsignificancetest standarddeviationacrossalltypesofcross validation whichiscausedbytherelativelysmallnumberofparticipant inorderto comparemultiplemodelsovercross validationsetsandshowthestatisticalsignificanceofourproposedmodel weperformthe friedmantesttofirstrejectthenullhypothesis wethenperformedapairwisestatisticalcomparison thewilcoxonsigne rankt pdparticipantroomtransitiondurationwithonandoffmedicationscomparisonusingwilcoxonsignedranktest offtransition meantransitionduration ontransition meantransitionduration w z kitchen livingoff kitchen livingon dining kitchenoff dining kitchenon dining livingoff dining livingon introduction relate work cohort dataset methodology framework modality positional embed collection rssi signal xr xr t rt accelerometer data xa xa t rt t time unit xr t xr tr represent rssi signal r access point xa t xa ta represent accelerometer datum spatial direction time t t give feature vector xt xr t u r represent rssi accelerometer datum time t t t represent time index positional embed hu t rssi accelerometer t wuxu t bu τt wu bu rd weight bias learn d embed dimension τt rd corresponding locality enhancement self attention self attention layer dual convolutional self attention dcsa dcsa take primary input secondary input yield grn gate residual network integrate dual input integrate embed norm standard layer normalization sa scale dot product self attention φk convolutional layer kernel size k stride wk wq wv weight key query value self attention layer d multihead dual convolutional self attention aim allow asymmetric long term learning multihead dcsa take input yield φki convolutional layer kernel size ki stride ki wk wq wv weight key query value self attention layer ξn concatenate output dcsaki temporal follow modality positional embed layer subsection positional embedding rssi hr hr t accelerometer ha ha t produce eq feed mdcsa layer kernel size kn h ha yield h ht ht rd t final layer loss calculation ˆyt wpht bp wp bp rm weight bias learn m number room location h ht rt classification produce linear layer apply refined embed ht ˆft wfht bf wf bf r weight bias learn ˆf ˆft rt target probability produce equation prior prediction output ˆe train generate likelihood estimate room prediction binary classification output ˆf ˆft train probability estimate particular room y ˆf f y lbce ˆft ft y lbce ˆf f ft log ˆft ft ˆft lll represent negative log likelihood lbce denote binary cross entropy y yt rt actual room location f ft rt binary value time t room referenced room denote conditional probability denote transition matrix cost having transition yt experiment result t accelerometer xa t feature combine parameter model parameter tune embed dimension d number epoch experimental result conclusion training model room level localisation medication state precision score auroc rf tener dtml alt dtml rssi rssi demographic feature n mdcsa rssi mdcsa mdcsa datum model kitch livin kitch dinin dinin livin ground truth transition mean transition duration transition w z kitchen live sec kitchen living sec dining kitchen sec dining kitchen sec dining living sec dining living sec
