{"status": "completed", "progress": 100, "message": "Processing completed successfully", "result": {"paper_id": "R011_3", "publishable": true, "confidence": 0.81, "conference": "General Conference", "rationale": "Error in recommendation process: could not convert string to float: 'We implement the state-of-the-art LightGCN to instantiate PAAC  aiming to investigate how it alleviates popularity bias. We\\ncompare PAAC with several debiased baselines  including re-weighting-based models  decorrelation-based models  and contrastive\\nlearning-based models.\\nWe utilize three widely used metrics  namely Recall@K  HR@K  and NDCG@K  to evaluate the performance of Top-K recommen-\\ndation. Recall@K and HR@K assess the number of target items retrieved in the recommendation results  emphasizing coverage. In\\ncontrast  NDCG@K evaluates the positions of target items in the ranking list  with a focus on their positions in the list. We use\\nthe full ranking strategy  considering all non-interacted items as candidate items to avoid selection bias during the test stage. We\\nrepeated each experiment five times with different random seeds and reported the average scores.\\n3.2 Overall Performance\\nAs shown in Table 1  we compare our model with several baselines across three datasets. The best performance for each metric\\nis highlighted in bold  while the second best is underlined. Our model consistently outperforms all compared methods across all\\nmetrics in every dataset.\\n•Our proposed model PAAC consistently outperforms all baselines and significantly mitigates the popularity bias. Specif-\\nically  PAAC enhances LightGCN  achieving improvements of 282.65%  180.79%  and 82.89% in NDCG@20 on the\\nYelp2018  Gowalla  and Amazon-Book datasets  respectively. Compared to the strongest baselines  PAAC delivers better\\nperformance. The most significant improvements are observed on Yelp2018  where our model achieves an 8.70% increase\\nin Recall@20  a 10.81% increase in HR@20  and a 30.2% increase in NDCG@20. This improvement can be attributed\\nto our use of popularity-aware supervised alignment to enhance the representation of less popular items and re-weighted\\ncontrastive learning to address representation separation from a popularity-centric perspective.\\n•The performance improvements of PAAC are smaller on sparser datasets. For example  on the Gowalla dataset  the\\nimprovements in Recall@20  HR@20  and NDCG@20 are 3.18%  5.85%  and 5.47%  respectively. This may be because \\nin sparser datasets like Gowalla  even popular items are not well-represented due to lower data density. Aligning unpopular\\nitems with these poorly represented popular items can introduce noise into the model. Therefore  the benefits of using\\nsupervisory signals for unpopular items may be reduced in very sparse environments  leading to smaller performance\\nimprovements.\\n•Regarding the baselines for mitigating popularity bias  the improvement of some is relatively limited compared to the\\nbackbone model (LightGCN) and even performs worse in some cases. This may be because some are specifically designed\\nfor traditional data-splitting scenarios  where the test set still follows a long-tail distribution  leading to poor generalization.\\nSome mitigate popularity bias by excluding item popularity information. Others use invariant learning to remove popularity\\ninformation at the representation level  generally performing better than the formers. This shows the importance of\\naddressing popularity bias at the representation level. Some outperform the other baselines  emphasizing the necessary to\\nimprove item representation consistency for mitigating popularity bias.\\n•Different metrics across various datasets show varying improvements in model performance. This suggests that different\\ndebiasing methods may need distinct optimization strategies for models. Additionally  we observe varying effects of PAAC\\nacross different datasets. This difference could be due to the sparser nature of the Gowalla dataset. Conversely  our model\\ncan directly provide supervisory signals for unpopular items and conduct intra-group optimization  consistently maintaining\\noptimal performance across all metrics on the three datasets.\\n3.3 Ablation Study\\nTo better understand the effectiveness of each component in PAAC  we conduct ablation studies on three datasets. Table 2 presents a\\ncomparison between PAAC and its variants on recommendation performance. Specifically  PAAC-w/o P refers to the variant where\\nthe re-weighting contrastive loss of popular items is removed  focusing instead on optimizing the consistency of representations for\\nunpopular items. Similarly  PAAC-w/o U denotes the removal of the re-weighting contrastive loss for unpopular items. PAAC-w/o\\nA refers to the variant without the popularity-aware supervised alignment loss. It’s worth noting that PAAC-w/o A differs from\\n4\\nTable 1: Performance comparison on three public datasets with K = 20. The best performance is indicated in bold  while the\\nsecond-best performance is underlined. The superscripts * indicate p ≤0.05 for the paired t-test of PAAC vs. the best baseline (the\\nrelative improvements are denoted as Imp.).\\n!ModelYelp2018 Gowalla Amazon-book\\nRecall@20 HR@20 NDCG@20 Recall@20 HR@20 NDCG@20 Recall@20 HR@20 NDCG@20\\nMF 0.0050 0.0109 0.0093 0.0343 0.0422 0.0280 0.0370 0.0388 0.0270\\nLightGCN 0.0048 0.0111 0.0098 0.0380 0.0468 0.0302 0.0421 0.0439 0.0304\\nIPS 0.0104 0.0183 0.0158 0.0562 0.0670 0.0444 0.0488 0.0510 0.0365\\nMACR 0.0402 0.0312 0.0265 0.0908 0.1086 0.0600 0.0515 0.0609 0.0487\\nα-Adjnorm 0.0053 0.0088 0.0080 0.0328 0.0409 0.0267 0.0422 0.0450 0.0264\\nInvCF 0.0444 0.0344 0.0291 0.1001 0.1202 0.0662 0.0562 0.0665 0.0515\\nAdap- τ 0.0450 0.0497 0.0341 0.1182 0.1248 0.0794 0.0641 0.0678 0.0511\\nSimGCL 0.0449 0.0518 0.0345 0.1194 0.1228 0.0804 0.0628 0.0648 0.0525\\nPAAC 0.0494* 0.0574* 0.0375* 0.1232* 0.1321* 0.0848* 0.0701* 0.0724* 0.0556*\\nImp. ***** % +10.81% *****% *****% *****% *****% *****% *****% 5.90%\\nSimGCL in that we split the contrastive loss on the item side  LCL\\nitem  into two distinct losses: LCL\\npopandLCL\\nunpop . This approach\\nallows us to separately address the consistency of popular and unpopular item representations  thereby providing a more detailed\\nanalysis of the impact of each component on the overall performance.\\nFrom Table 2  we observe that PAAC-w/o A outperforms SimGCL in most cases. This validates that re-weighting the importance of\\npopular and unpopular items can effectively improve the model’s performance in alleviating popularity bias. It also demonstrates the\\neffectiveness of using supervision signals from popular items to enhance the representations of unpopular items  providing more\\nopportunities for future research on mitigating popularity bias. Moreover  compared with PAAC-w/o U  PAAC-w/o P results in much\\nworse performance. This confirms the importance of re-weighting popular items in contrastive learning for mitigating popularity\\nbias. Finally  PAAC consistently outperforms the three variants  demonstrating the effectiveness of combining supervised alignment\\nand re-weighting contrastive learning. Based on the above analysis  we conclude that leveraging supervisory signals from popular\\nitem representations can better optimize representations for unpopular items  and re-weighting contrastive learning allows the model\\nto focus on more informative or critical samples  thereby improving overall performance. All the proposed modules significantly\\ncontribute to alleviating popularity bias.\\nTable 2: Ablation study of PAAC  highlighting the best-performing model on each dataset and metrics in bold. Specifically \\nPAAC-w/o P removes the re-weighting contrastive loss of popular items  PAAC-w/o U eliminates the re-weighting contrastive loss\\nof unpopular items  and PAAC-w/o A omits the popularity-aware supervised alignment loss.\\nPAAC-w/o P 0.0443 0.0536 0.0340 0.1098 0.1191 0.0750 0.0616 0.0639 0.0458\\nPAAC-w/o U 0.0462 0.0545 0.0358 0.1120 0.1179 0.0752 0.0594 0.0617 0.0464\\nPAAC-w/o A 0.0466 0.0547 0.0360 0.1195 0.1260 0.0815 0.0687 0.0711 0.0536\\n3.4 Debias Ability\\nTo further verify the effectiveness of PAAC in alleviating popularity bias  we conduct a comprehensive analysis focusing on the\\nrecommendation performance across different popularity item groups. Specifically  20% of the most popular items are labeled\\n’Popular’  and the rest are labeled ’Unpopular’. We compare the performance of PAAC with LightGCN  IPS  MACR  and SimGCL\\nusing the NDCG@20 metric across different popularity groups. We use ∆to denote the accuracy gap between the two groups. We\\ndraw the following'"}}