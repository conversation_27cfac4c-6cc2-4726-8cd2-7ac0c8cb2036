{"version": 2, "name": "enhanced-research-paper-classification", "builds": [{"src": "src/enhanced_app.py", "use": "@vercel/python", "config": {"maxLambdaSize": "50mb"}}], "routes": [{"src": "/static/(.*)", "dest": "/frontend/static/$1"}, {"src": "/(.*)", "dest": "src/enhanced_app.py"}], "env": {"GROQ_API_KEY": "@groq_api_key"}, "functions": {"src/enhanced_app.py": {"maxDuration": 300}}, "regions": ["iad1"], "installCommand": "pip install -r requirements.txt", "buildCommand": "echo 'Build complete'", "outputDirectory": ".", "framework": null}