# Enhanced Research Paper Classification - Deployment Guide

## Overview

This guide provides comprehensive instructions for deploying the Enhanced Research Paper Classification system with Groq LLM agent integration. The system can be deployed on Vercel for production use or run locally for development.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Local Development Setup](#local-development-setup)
3. [Vercel Deployment](#vercel-deployment)
4. [Environment Configuration](#environment-configuration)
5. [Testing the Deployment](#testing-the-deployment)
6. [Troubleshooting](#troubleshooting)
7. [API Documentation](#api-documentation)

## Prerequisites

### Required Accounts and Services

1. **Groq API Account**
   - Sign up at [https://console.groq.com/](https://console.groq.com/)
   - Obtain your API key from the dashboard
   - Ensure you have sufficient credits for LLM usage

2. **Vercel Account** (for production deployment)
   - Sign up at [https://vercel.com/](https://vercel.com/)
   - Connect your GitHub account for easy deployment

3. **GitHub Repository**
   - Fork or clone this repository to your GitHub account

### System Requirements

- Python 3.11 or higher
- Node.js 18+ (for Vercel CLI)
- Git

## Local Development Setup

### 1. Clone the Repository

```bash
git clone <your-repository-url>
cd Research-Paper-classification-MVP-main
```

### 2. Create Virtual Environment

```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

### 3. Install Dependencies

```bash
pip install -r requirements.txt
```

### 4. Set Up Environment Variables

```bash
cp .env.example .env
```

Edit the `.env` file and add your Groq API key:

```env
GROQ_API_KEY=your_actual_groq_api_key_here
FLASK_ENV=development
FLASK_DEBUG=True
PORT=5000
```

### 5. Prepare Required Data

Ensure the following directories and files exist:

```
data/
├── processed/
│   └── metadata_reference.csv
├── raw/
│   └── papers/
└── task1_prediction/

models/
└── publishability_model.joblib  # or .pkl

results/
```

### 6. Run the Application

```bash
cd src
python enhanced_app.py
```

The application will be available at `http://localhost:5000`

## Vercel Deployment

### Method 1: Vercel CLI (Recommended)

1. **Install Vercel CLI**
   ```bash
   npm install -g vercel
   ```

2. **Login to Vercel**
   ```bash
   vercel login
   ```

3. **Deploy from Project Root**
   ```bash
   vercel
   ```

4. **Set Environment Variables**
   ```bash
   vercel env add GROQ_API_KEY
   ```
   Enter your Groq API key when prompted.

5. **Deploy to Production**
   ```bash
   vercel --prod
   ```

### Method 2: GitHub Integration

1. **Push to GitHub**
   ```bash
   git add .
   git commit -m "Enhanced research paper classification with LLM"
   git push origin main
   ```

2. **Import to Vercel**
   - Go to [Vercel Dashboard](https://vercel.com/dashboard)
   - Click "New Project"
   - Import your GitHub repository
   - Configure environment variables (see below)
   - Deploy

### Method 3: Vercel Dashboard Upload

1. **Create Deployment Package**
   ```bash
   zip -r enhanced-paper-classification.zip . -x "*.git*" "venv/*" "__pycache__/*" "*.pyc"
   ```

2. **Upload to Vercel**
   - Go to Vercel Dashboard
   - Click "New Project"
   - Upload the zip file
   - Configure settings and deploy

## Environment Configuration

### Required Environment Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `GROQ_API_KEY` | Your Groq API key for LLM functionality | `gsk_...` |

### Optional Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `FLASK_ENV` | Flask environment | `production` |
| `FLASK_DEBUG` | Debug mode | `False` |
| `PORT` | Application port | `5000` |

### Setting Environment Variables in Vercel

#### Via CLI:
```bash
vercel env add GROQ_API_KEY
vercel env add FLASK_ENV production
```

#### Via Dashboard:
1. Go to your project in Vercel Dashboard
2. Navigate to Settings → Environment Variables
3. Add each variable with appropriate values
4. Redeploy the project

## Testing the Deployment

### 1. Basic Functionality Test

1. **Upload Test**
   - Navigate to your deployed URL
   - Upload a sample PDF research paper
   - Verify the upload process completes

2. **Basic Classification Test**
   - Ensure the basic ML classification works
   - Check that results are displayed correctly

3. **LLM Integration Test**
   - Verify that AI analysis appears in results
   - Test improvement suggestions functionality
   - Check journal recommendations

### 2. API Endpoint Testing

Test the enhanced API endpoints:

```bash
# Test paper analysis
curl -X POST https://your-app.vercel.app/api/analyze-paper \
  -H "Content-Type: application/json" \
  -d '{"paper_content": "Sample paper content..."}'

# Test improvement suggestions
curl -X POST https://your-app.vercel.app/api/get-improvement-suggestions \
  -H "Content-Type: application/json" \
  -d '{"paper_content": "Sample paper content..."}'

# Test journal comparison
curl -X POST https://your-app.vercel.app/api/compare-journals \
  -H "Content-Type: application/json" \
  -d '{"paper_content": "Sample paper content...", "target_journals": ["ICML", "NeurIPS"]}'
```

### 3. Performance Testing

- Test with various PDF sizes (up to 16MB)
- Verify processing times are reasonable
- Check memory usage during analysis

## Troubleshooting

### Common Issues and Solutions

#### 1. Groq API Key Issues

**Problem**: "LLM agent not available" error
**Solution**: 
- Verify your Groq API key is correctly set
- Check API key has sufficient credits
- Ensure key has proper permissions

#### 2. File Upload Issues

**Problem**: PDF upload fails
**Solution**:
- Check file size (max 16MB)
- Verify file is valid PDF format
- Check upload folder permissions

#### 3. Model Loading Issues

**Problem**: "Could not load classification model" error
**Solution**:
- Ensure `publishability_model.joblib` exists in `models/` directory
- Check model file is not corrupted
- Verify model was trained with compatible scikit-learn version

#### 4. Memory Issues on Vercel

**Problem**: Function timeout or memory errors
**Solution**:
- Optimize PDF processing for large files
- Consider implementing file size limits
- Use streaming for large responses

#### 5. CORS Issues

**Problem**: Frontend can't access API endpoints
**Solution**:
- Verify CORS is properly configured in `enhanced_app.py`
- Check that origins are correctly set
- Ensure preflight requests are handled

### Debug Mode

For debugging deployment issues:

1. **Enable Debug Logging**
   ```python
   import logging
   logging.basicConfig(level=logging.DEBUG)
   ```

2. **Check Vercel Function Logs**
   ```bash
   vercel logs
   ```

3. **Local Testing with Production Settings**
   ```bash
   FLASK_ENV=production python src/enhanced_app.py
   ```

## API Documentation

### Enhanced Endpoints

#### POST /api/analyze-paper
Comprehensive paper analysis using LLM.

**Request Body:**
```json
{
  "paper_content": "Full paper text...",
  "metadata": {
    "title": "Paper Title",
    "authors": ["Author 1", "Author 2"]
  }
}
```

**Response:**
```json
{
  "publishability_score": 8.5,
  "publishability_status": "publishable",
  "strengths": ["Novel approach", "Strong experiments"],
  "weaknesses": ["Limited theoretical analysis"],
  "improvement_suggestions": [...],
  "journal_recommendations": [...],
  "comparative_analysis": "..."
}
```

#### POST /api/get-improvement-suggestions
Generate specific improvement recommendations.

**Request Body:**
```json
{
  "paper_content": "Full paper text..."
}
```

#### POST /api/compare-journals
Compare suitability across multiple journals.

**Request Body:**
```json
{
  "paper_content": "Full paper text...",
  "target_journals": ["ICML", "NeurIPS", "ICLR"]
}
```

#### POST /api/refine-analysis
Refine analysis based on user questions.

**Request Body:**
```json
{
  "paper_content": "Full paper text...",
  "previous_analysis": {...},
  "user_query": "How can I improve the methodology?"
}
```

### Legacy Endpoints

All original endpoints remain functional:
- `POST /upload` - File upload and processing
- `GET /status/<submission_id>` - Processing status
- `GET /result/<submission_id>` - Analysis results

## Performance Optimization

### For Production Deployment

1. **Enable Caching**
   - Implement Redis for LLM response caching
   - Cache processed paper metadata

2. **Optimize File Processing**
   - Use streaming for large PDF files
   - Implement background processing for heavy tasks

3. **Monitor Usage**
   - Track Groq API usage and costs
   - Monitor Vercel function execution times
   - Set up alerts for errors

### Cost Management

1. **Groq API Optimization**
   - Cache LLM responses for identical content
   - Implement rate limiting for API calls
   - Use shorter prompts when possible

2. **Vercel Optimization**
   - Monitor function execution time
   - Optimize cold start performance
   - Use appropriate function regions

## Security Considerations

1. **API Key Security**
   - Never commit API keys to version control
   - Use Vercel environment variables
   - Rotate keys regularly

2. **Input Validation**
   - Validate all file uploads
   - Sanitize user inputs
   - Implement rate limiting

3. **Error Handling**
   - Don't expose sensitive information in errors
   - Log security events
   - Implement proper error responses

## Monitoring and Maintenance

1. **Health Checks**
   - Monitor API endpoint availability
   - Check LLM service status
   - Verify model loading

2. **Performance Monitoring**
   - Track response times
   - Monitor error rates
   - Analyze usage patterns

3. **Updates and Maintenance**
   - Keep dependencies updated
   - Monitor for security vulnerabilities
   - Update models as needed

## Support and Contact

For technical support or questions about deployment:

1. Check the troubleshooting section above
2. Review Vercel documentation for platform-specific issues
3. Check Groq documentation for API-related problems
4. Create an issue in the project repository

## License and Usage

This enhanced version includes advanced AI capabilities and should be used in accordance with:
- Groq API terms of service
- Vercel platform policies
- Any applicable research ethics guidelines

---

**Note**: This deployment guide assumes you have the necessary model files and reference data. Ensure all required files are present before deployment.

