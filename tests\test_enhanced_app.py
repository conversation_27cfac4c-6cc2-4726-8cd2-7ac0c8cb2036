"""
Test cases for the Enhanced Flask Application.

This module contains integration tests for the enhanced Flask application
with LLM agent integration, testing API endpoints and workflow functionality.
"""

import pytest
import os
import json
import tempfile
import sys
from unittest.mock import Mock, patch, MagicMock
from io import BytesIO

# Add src directory to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

# Import the enhanced app
import enhanced_app
from llm_agent import PaperAnalysis, ImprovementSuggestion, JournalRecommendation


class TestEnhancedFlaskApp:
    """Test cases for the enhanced Flask application."""
    
    @pytest.fixture
    def app(self):
        """Create test Flask app."""
        enhanced_app.app.config['TESTING'] = True
        enhanced_app.app.config['UPLOAD_FOLDER'] = tempfile.mkdtemp()
        enhanced_app.app.config['RESULTS_FOLDER'] = tempfile.mkdtemp()
        enhanced_app.app.config['MODELS_FOLDER'] = tempfile.mkdtemp()
        enhanced_app.app.config['PROCESSED_FOLDER'] = tempfile.mkdtemp()
        enhanced_app.app.config['TASK1_PREDICTION_FOLDER'] = tempfile.mkdtemp()
        return enhanced_app.app
    
    @pytest.fixture
    def client(self, app):
        """Create test client."""
        return app.test_client()
    
    @pytest.fixture
    def sample_pdf_content(self):
        """Create sample PDF content for testing."""
        # Create a simple PDF-like binary content
        return b'%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n'
    
    @pytest.fixture
    def mock_llm_analysis(self):
        """Mock LLM analysis result."""
        return PaperAnalysis(
            publishability_score=8.0,
            publishability_status="publishable",
            publishability_reasons=["Strong methodology", "Novel approach"],
            improvement_suggestions=[
                ImprovementSuggestion(
                    category="methodology",
                    priority="medium",
                    description="Add more statistical analysis",
                    specific_actions=["Include confidence intervals", "Add significance tests"],
                    impact="Stronger statistical foundation"
                )
            ],
            journal_recommendations=[
                JournalRecommendation(
                    journal_name="ICML",
                    conference_type="conference",
                    suitability_score=8.5,
                    reasons=["Strong ML focus", "Appreciates novel methods"],
                    requirements=["Novel contribution", "Strong experiments"],
                    typical_acceptance_criteria="High-quality ML research",
                    submission_tips=["Emphasize novelty", "Strong baselines"]
                )
            ],
            comparative_analysis="ICML is the best fit due to its focus on methodological contributions.",
            strengths=["Novel approach", "Strong experiments"],
            weaknesses=["Limited theoretical analysis"],
            novelty_assessment="High novelty with practical implications",
            technical_quality="Strong technical approach with proper validation",
            clarity_assessment="Well-written and clearly structured"
        )
    
    def test_index_route(self, client):
        """Test the main index route."""
        response = client.get('/')
        assert response.status_code == 200
        assert b'Enhanced Research Papers Classification' in response.data
    
    def test_upload_no_file(self, client):
        """Test upload endpoint with no file."""
        response = client.post('/upload')
        assert response.status_code == 400
        data = json.loads(response.data)
        assert 'error' in data
        assert 'No file part' in data['error']
    
    def test_upload_empty_filename(self, client):
        """Test upload endpoint with empty filename."""
        response = client.post('/upload', data={'file': (BytesIO(b''), '')})
        assert response.status_code == 400
        data = json.loads(response.data)
        assert 'error' in data
        assert 'No selected file' in data['error']
    
    def test_upload_invalid_file_type(self, client):
        """Test upload endpoint with invalid file type."""
        response = client.post('/upload', data={
            'file': (BytesIO(b'test content'), 'test.txt')
        })
        assert response.status_code == 400
        data = json.loads(response.data)
        assert 'error' in data
        assert 'File type not allowed' in data['error']
    
    @patch('enhanced_app.process_paper_enhanced')
    def test_upload_success(self, mock_process, client, sample_pdf_content):
        """Test successful file upload."""
        mock_process.return_value = True
        
        response = client.post('/upload', data={
            'file': (BytesIO(sample_pdf_content), 'test.pdf')
        })
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert 'submission_id' in data
        assert 'message' in data
        assert 'enhanced processing started' in data['message']
    
    def test_status_not_found(self, client):
        """Test status endpoint with non-existent submission ID."""
        response = client.get('/status/non-existent-id')
        assert response.status_code == 404
        data = json.loads(response.data)
        assert 'error' in data
        assert 'Submission ID not found' in data['error']
    
    def test_result_not_found(self, client):
        """Test result endpoint with non-existent submission ID."""
        response = client.get('/result/non-existent-id')
        assert response.status_code == 404
        data = json.loads(response.data)
        assert 'error' in data
        assert 'Result not found' in data['error']
    
    @patch('enhanced_app.llm_agent')
    def test_analyze_paper_endpoint_no_llm(self, mock_llm_agent, client):
        """Test analyze paper endpoint when LLM agent is not available."""
        mock_llm_agent = None
        
        response = client.post('/api/analyze-paper', 
                             json={'paper_content': 'test content'})
        assert response.status_code == 503
        data = json.loads(response.data)
        assert 'error' in data
        assert 'LLM agent not available' in data['error']
    
    @patch('enhanced_app.llm_agent')
    def test_analyze_paper_endpoint_no_content(self, mock_llm_agent, client):
        """Test analyze paper endpoint with no content."""
        mock_llm_agent.return_value = Mock()
        
        response = client.post('/api/analyze-paper', json={})
        assert response.status_code == 400
        data = json.loads(response.data)
        assert 'error' in data
        assert 'Paper content is required' in data['error']
    
    @patch('enhanced_app.llm_agent')
    def test_analyze_paper_endpoint_success(self, mock_llm_agent, client, mock_llm_analysis):
        """Test successful paper analysis endpoint."""
        mock_agent = Mock()
        mock_agent.analyze_paper.return_value = mock_llm_analysis
        mock_llm_agent = mock_agent
        
        # Patch the global llm_agent variable
        with patch.object(enhanced_app, 'llm_agent', mock_agent):
            response = client.post('/api/analyze-paper', json={
                'paper_content': 'test content',
                'metadata': {'title': 'Test Paper'}
            })
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert 'publishability_score' in data
        assert data['publishability_score'] == 8.0
        assert 'improvement_suggestions' in data
        assert len(data['improvement_suggestions']) == 1
    
    @patch('enhanced_app.llm_agent')
    def test_get_improvement_suggestions_endpoint(self, mock_llm_agent, client):
        """Test improvement suggestions endpoint."""
        mock_agent = Mock()
        mock_suggestion = ImprovementSuggestion(
            category="writing",
            priority="high",
            description="Improve clarity",
            specific_actions=["Use simpler language"],
            impact="Better readability"
        )
        mock_agent.suggest_improvements.return_value = [mock_suggestion]
        
        with patch.object(enhanced_app, 'llm_agent', mock_agent):
            response = client.post('/api/get-improvement-suggestions', json={
                'paper_content': 'test content'
            })
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert 'suggestions' in data
        assert len(data['suggestions']) == 1
        assert data['suggestions'][0]['category'] == 'writing'
    
    @patch('enhanced_app.llm_agent')
    def test_compare_journals_endpoint(self, mock_llm_agent, client):
        """Test journal comparison endpoint."""
        mock_agent = Mock()
        mock_recommendation = JournalRecommendation(
            journal_name="ICML",
            conference_type="conference",
            suitability_score=8.5,
            reasons=["Strong ML focus"],
            requirements=["Novel contribution"],
            typical_acceptance_criteria="High quality research",
            submission_tips=["Emphasize novelty"]
        )
        mock_agent.compare_journals.return_value = [mock_recommendation]
        
        with patch.object(enhanced_app, 'llm_agent', mock_agent):
            response = client.post('/api/compare-journals', json={
                'paper_content': 'test content',
                'target_journals': ['ICML', 'NeurIPS']
            })
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert 'recommendations' in data
        assert len(data['recommendations']) == 1
        assert data['recommendations'][0]['journal_name'] == 'ICML'
    
    @patch('enhanced_app.llm_agent')
    def test_refine_analysis_endpoint(self, mock_llm_agent, client):
        """Test analysis refinement endpoint."""
        mock_agent = Mock()
        mock_agent.refine_analysis.return_value = "The methodology could be improved by adding more statistical analysis."
        
        with patch.object(enhanced_app, 'llm_agent', mock_agent):
            response = client.post('/api/refine-analysis', json={
                'paper_content': 'test content',
                'previous_analysis': {},
                'user_query': 'How can I improve the methodology?'
            })
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert 'refined_analysis' in data
        assert 'methodology' in data['refined_analysis']
    
    def test_visualizations_endpoint(self, client):
        """Test visualizations endpoint."""
        response = client.get('/visualizations')
        assert response.status_code == 200
        data = json.loads(response.data)
        assert isinstance(data, list)
    
    def test_about_route(self, client):
        """Test about page route."""
        response = client.get('/about')
        assert response.status_code == 200
    
    def test_documentation_route(self, client):
        """Test documentation page route."""
        response = client.get('/documentation')
        assert response.status_code == 200


class TestEnhancedProcessing:
    """Test cases for enhanced processing functionality."""
    
    @pytest.fixture
    def mock_dependencies(self):
        """Mock all external dependencies."""
        with patch.multiple(
            'enhanced_app',
            extract_text_combined=Mock(return_value="Sample paper text"),
            parse_sections=Mock(return_value={
                'abstract': 'Sample abstract',
                'introduction': 'Sample introduction',
                'methodology': 'Sample methodology',
                'results': 'Sample results',
                'conclusion': 'Sample conclusion'
            }),
            generate_metadata_embedded=Mock(),
            safe_load_model=Mock(return_value=(Mock(), None)),
            recommend_journal_self_contained=Mock(return_value=("ICML", "Good fit"))
        ) as mocks:
            yield mocks
    
    @patch('enhanced_app.pd.read_csv')
    @patch('enhanced_app.TfidfVectorizer')
    def test_process_paper_enhanced_success(self, mock_vectorizer, mock_read_csv, mock_dependencies):
        """Test successful enhanced paper processing."""
        # Setup mocks
        mock_read_csv.return_value = Mock()
        mock_vectorizer.return_value = Mock()
        
        # Mock metadata generation
        mock_metadata_df = Mock()
        mock_metadata_df.apply.return_value = Mock()
        mock_dependencies['generate_metadata_embedded'].return_value = mock_metadata_df
        
        # Mock model prediction
        mock_model = Mock()
        mock_model.predict.return_value = [1]  # Publishable
        mock_model.predict_proba.return_value = [[0.2, 0.8]]  # 80% confidence
        mock_dependencies['safe_load_model'].return_value = (mock_model, None)
        
        # Mock LLM agent
        mock_llm_agent = Mock()
        mock_analysis = Mock()
        mock_analysis.publishability_score = 8.0
        mock_analysis.publishability_status = "publishable"
        mock_analysis.strengths = ["Strong approach"]
        mock_analysis.weaknesses = ["Minor issues"]
        mock_analysis.improvement_suggestions = []
        mock_analysis.journal_recommendations = []
        mock_llm_agent.analyze_paper.return_value = mock_analysis
        
        with patch.object(enhanced_app, 'llm_agent', mock_llm_agent):
            with patch('enhanced_app.pd.concat', return_value=mock_metadata_df):
                with patch('enhanced_app.save_status'):
                    result = enhanced_app.process_paper_enhanced("test.pdf", "test_id")
        
        assert result is True
    
    def test_process_paper_enhanced_failure(self, mock_dependencies):
        """Test enhanced paper processing with failure."""
        # Make text extraction fail
        mock_dependencies['extract_text_combined'].return_value = ""
        
        with patch('enhanced_app.save_status'):
            result = enhanced_app.process_paper_enhanced("test.pdf", "test_id")
        
        assert result is False
    
    def test_save_status_success(self):
        """Test successful status saving."""
        test_status = {
            "status": "processing",
            "progress": 50,
            "message": "Test message"
        }
        
        enhanced_app.processing_status["test_id"] = test_status
        
        with patch('builtins.open', create=True) as mock_open:
            with patch('json.dump') as mock_json_dump:
                enhanced_app.save_status("test_id")
                mock_open.assert_called_once()
                mock_json_dump.assert_called_once_with(test_status, mock_open().__enter__())
    
    def test_save_status_nonexistent_id(self):
        """Test status saving with non-existent ID."""
        with patch('enhanced_app.logger') as mock_logger:
            enhanced_app.save_status("nonexistent_id")
            mock_logger.warning.assert_called_once()


class TestUtilityFunctions:
    """Test cases for utility functions."""
    
    def test_allowed_file_pdf(self):
        """Test allowed file function with PDF."""
        assert enhanced_app.allowed_file("test.pdf") is True
        assert enhanced_app.allowed_file("test.PDF") is True
    
    def test_allowed_file_non_pdf(self):
        """Test allowed file function with non-PDF."""
        assert enhanced_app.allowed_file("test.txt") is False
        assert enhanced_app.allowed_file("test.doc") is False
        assert enhanced_app.allowed_file("test") is False
    
    def test_compute_similarity_success(self):
        """Test text similarity computation."""
        # Mock vectorizer
        mock_vectorizer = Mock()
        mock_vectorizer.transform.return_value.toarray.return_value = [[0.5, 0.3, 0.2]]
        enhanced_app.vectorizer = mock_vectorizer
        
        with patch('enhanced_app.cosine_similarity', return_value=[[0.8]]):
            similarity = enhanced_app.compute_similarity("text1", "text2")
            assert similarity == 0.8
    
    def test_compute_similarity_no_vectorizer(self):
        """Test text similarity computation without vectorizer."""
        enhanced_app.vectorizer = None
        similarity = enhanced_app.compute_similarity("text1", "text2")
        assert similarity == 0
    
    def test_compute_similarity_empty_text(self):
        """Test text similarity computation with empty text."""
        enhanced_app.vectorizer = Mock()
        similarity = enhanced_app.compute_similarity("", "text2")
        assert similarity == 0


class TestInitialization:
    """Test cases for application initialization."""
    
    @patch('enhanced_app.os.getenv')
    @patch('enhanced_app.GroqLLMAgent')
    def test_initialize_llm_agent_success(self, mock_agent_class, mock_getenv):
        """Test successful LLM agent initialization."""
        mock_getenv.return_value = "test_api_key"
        mock_agent = Mock()
        mock_agent_class.return_value = mock_agent
        
        enhanced_app.initialize_llm_agent()
        
        mock_agent_class.assert_called_once_with(api_key="test_api_key")
        assert enhanced_app.llm_agent == mock_agent
    
    @patch('enhanced_app.os.getenv')
    def test_initialize_llm_agent_no_key(self, mock_getenv):
        """Test LLM agent initialization without API key."""
        mock_getenv.return_value = None
        
        with patch('enhanced_app.logger') as mock_logger:
            enhanced_app.initialize_llm_agent()
            mock_logger.warning.assert_called_once()
            assert enhanced_app.llm_agent is None
    
    @patch('enhanced_app.os.getenv')
    @patch('enhanced_app.GroqLLMAgent')
    def test_initialize_llm_agent_failure(self, mock_agent_class, mock_getenv):
        """Test LLM agent initialization failure."""
        mock_getenv.return_value = "test_api_key"
        mock_agent_class.side_effect = Exception("Initialization failed")
        
        with patch('enhanced_app.logger') as mock_logger:
            enhanced_app.initialize_llm_agent()
            mock_logger.error.assert_called_once()
            assert enhanced_app.llm_agent is None


if __name__ == "__main__":
    pytest.main([__file__, "-v"])

