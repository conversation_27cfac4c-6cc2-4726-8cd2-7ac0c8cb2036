# Python
__pycache__/
*.py[cod]
*.so
*.egg
*.egg-info/
dist/
build/

# Jupyter Notebooks
.ipynb_checkpoints

# Environments
venv/
venvv/
.env
.env.*

# VSCode
.vscode/

# Data and results
*.log
*.tsv
*.xlsx
*.h5
*.db
*.sqlite3
*.npy
*.npz
*.parquet
*.feather

# Do NOT ignore .csv, .pkl, .joblib, .sav, .pdf, .docx, .pptx, .zip, .tar.gz, .tar, .rar, .7z, or any files in results/, models/, or data/
# (Allow large files to be tracked if needed)

# Ignore reports and pipeline flow
PIPELINE_FLOW.md
final_report.md

# OS files
.DS_Store
Thumbs.db

# License (will be added directly in git)
LICENSE
