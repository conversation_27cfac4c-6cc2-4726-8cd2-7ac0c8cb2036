# LLM Agent Setup Guide

## Overview
The enhanced research paper classification system includes an LLM (Large Language Model) agent powered by Groq API that provides detailed analysis, improvement suggestions, and journal recommendations.

## Current Issues
1. **LLM Agent Not Working**: The `GROQ_API_KEY` environment variable is not set
2. **Confidence Score Display**: Fixed NaN% display issue

## Setup Instructions

### 1. Get a Groq API Key
1. Visit [Groq Console](https://console.groq.com/)
2. Sign up for a free account
3. Navigate to API Keys section
4. Create a new API key
5. Copy the API key (it starts with `gsk_`)

### 2. Set Environment Variable

#### On Windows (PowerShell):
```powershell
$env:GROQ_API_KEY="your-api-key-here"
```

#### On Windows (Command Prompt):
```cmd
set GROQ_API_KEY=your-api-key-here
```

#### On Linux/Mac:
```bash
export GROQ_API_KEY="your-api-key-here"
```

### 3. Test the Setup
Run the test script to verify everything is working:
```bash
python test_llm_agent.py
```

### 4. Start the Application
```bash
python src/enhanced_app.py
```

## Features Available with LLM Agent

### When LLM Agent is Working:
- ✅ Detailed publishability analysis (0-10 score)
- ✅ Strengths and weaknesses identification
- ✅ Specific improvement suggestions
- ✅ Journal recommendations with suitability scores
- ✅ Comparative analysis of different venues
- ✅ Interactive AI queries

### When LLM Agent is Not Available:
- ✅ Basic classification (publishable/not publishable)
- ✅ Confidence score
- ✅ Basic journal recommendation
- ❌ Detailed AI analysis
- ❌ Improvement suggestions
- ❌ Interactive AI features

## Troubleshooting

### Issue: "GROQ_API_KEY not found"
**Solution**: Set the environment variable as shown above

### Issue: "LLM analysis failed"
**Solution**: Check your internet connection and API key validity

### Issue: Confidence score shows "NaN%"
**Solution**: This has been fixed in the latest code

### Issue: LLM analysis section not showing
**Solution**: Ensure the API key is set and the agent initializes successfully

## API Usage
The Groq API has generous free tier limits:
- Free tier: 1000 requests per day
- Rate limit: 100 requests per minute
- Model: Llama 3.1 70B (high quality)

## Security Notes
- Never commit your API key to version control
- Use environment variables for production deployment
- The API key is only used for LLM analysis and is not stored

## Support
If you encounter issues:
1. Check the application logs for error messages
2. Verify your API key is correct
3. Test with the provided test script
4. Ensure all dependencies are installed: `pip install -r requirements.txt` 