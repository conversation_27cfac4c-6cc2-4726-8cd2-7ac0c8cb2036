// Enhanced JavaScript for AI-Powered Research Paper Classification

class EnhancedPaperAnalyzer {
    constructor() {
        this.currentSubmissionId = null;
        this.currentResult = null;
        this.processingSteps = [
            { id: 'step-1', progress: 20, message: 'Extracting text from PDF...' },
            { id: 'step-2', progress: 45, message: 'Analyzing paper features...' },
            { id: 'step-3', progress: 70, message: 'Running AI analysis...' },
            { id: 'step-4', progress: 100, message: 'Compiling results...' }
        ];
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupDragAndDrop();
    }

    setupEventListeners() {
        // File input
        document.getElementById('file-input').addEventListener('change', (e) => {
            this.handleFileSelect(e.target.files[0]);
        });

        // Upload button
        document.getElementById('upload-btn').addEventListener('click', () => {
            this.uploadFile();
        });

        // Remove file button
        document.getElementById('remove-file').addEventListener('click', () => {
            this.removeFile();
        });

        // Analyze another button
        document.getElementById('analyze-another-btn').addEventListener('click', () => {
            this.resetForm();
        });

        // Ask AI button
        document.getElementById('ask-ai-btn').addEventListener('click', () => {
            this.askAI();
        });

        // Download report button
        document.getElementById('download-report-btn').addEventListener('click', () => {
            this.downloadReport();
        });

        // Enter key for AI query
        document.getElementById('user-query').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.askAI();
            }
        });
    }

    setupDragAndDrop() {
        const dropArea = document.getElementById('drop-area');

        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropArea.addEventListener(eventName, this.preventDefaults, false);
        });

        ['dragenter', 'dragover'].forEach(eventName => {
            dropArea.addEventListener(eventName, () => {
                dropArea.classList.add('dragover');
            }, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            dropArea.addEventListener(eventName, () => {
                dropArea.classList.remove('dragover');
            }, false);
        });

        dropArea.addEventListener('drop', (e) => {
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                this.handleFileSelect(files[0]);
            }
        }, false);
    }

    preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    handleFileSelect(file) {
        if (!file) return;

        if (file.type !== 'application/pdf') {
            this.showAlert('Please select a PDF file.', 'danger');
            return;
        }

        if (file.size > 16 * 1024 * 1024) {
            this.showAlert('File size must be less than 16MB.', 'danger');
            return;
        }

        this.selectedFile = file;
        this.displaySelectedFile(file);
        document.getElementById('upload-btn').disabled = false;
    }

    displaySelectedFile(file) {
        const selectedFileDiv = document.getElementById('selected-file');
        const fileName = document.getElementById('file-name');
        const fileSize = document.getElementById('file-size');

        fileName.textContent = file.name;
        fileSize.textContent = this.formatFileSize(file.size);
        selectedFileDiv.classList.remove('d-none');
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    removeFile() {
        this.selectedFile = null;
        document.getElementById('selected-file').classList.add('d-none');
        document.getElementById('file-input').value = '';
        document.getElementById('upload-btn').disabled = true;
    }

    async uploadFile() {
        if (!this.selectedFile) return;

        const formData = new FormData();
        formData.append('file', this.selectedFile);

        try {
            this.showProcessingSection();
            
            const response = await fetch('/upload', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (response.ok) {
                this.currentSubmissionId = result.submission_id;
                this.pollStatus();
            } else {
                throw new Error(result.error || 'Upload failed');
            }
        } catch (error) {
            this.showAlert(`Upload failed: ${error.message}`, 'danger');
            this.resetForm();
        }
    }

    showProcessingSection() {
        document.getElementById('upload-section').classList.add('d-none');
        document.getElementById('processing-section').classList.remove('d-none');
        document.getElementById('results-section').classList.add('d-none');
    }

    async pollStatus() {
        if (!this.currentSubmissionId) return;

        try {
            const response = await fetch(`/status/${this.currentSubmissionId}`);
            const status = await response.json();

            if (response.ok) {
                this.updateProgress(status);

                if (status.status === 'completed') {
                    await this.loadResults();
                } else if (status.status === 'error') {
                    throw new Error(status.message || 'Processing failed');
                } else {
                    // Continue polling
                    setTimeout(() => this.pollStatus(), 2000);
                }
            } else {
                throw new Error('Failed to get status');
            }
        } catch (error) {
            this.showAlert(`Processing failed: ${error.message}`, 'danger');
            this.resetForm();
        }
    }

    updateProgress(status) {
        const progressBar = document.getElementById('progress-bar');
        const progressText = document.getElementById('progress-text');
        const processingMessage = document.getElementById('processing-message');

        progressBar.style.width = `${status.progress}%`;
        progressText.textContent = `${status.progress}%`;
        processingMessage.textContent = status.message;

        // Update step indicators
        this.processingSteps.forEach((step, index) => {
            const stepElement = document.getElementById(step.id);
            if (status.progress >= step.progress) {
                stepElement.classList.add('completed');
                stepElement.classList.remove('active');
            } else if (status.progress >= (index > 0 ? this.processingSteps[index - 1].progress : 0)) {
                stepElement.classList.add('active');
                stepElement.classList.remove('completed');
            }
        });
    }

    async loadResults() {
        try {
            const response = await fetch(`/result/${this.currentSubmissionId}`);
            const result = await response.json();

            if (response.ok) {
                this.currentResult = result;
                this.displayResults(result);
            } else {
                throw new Error('Failed to load results');
            }
        } catch (error) {
            this.showAlert(`Failed to load results: ${error.message}`, 'danger');
            this.resetForm();
        }
    }

    displayResults(result) {
        document.getElementById('processing-section').classList.add('d-none');
        document.getElementById('results-section').classList.remove('d-none');

        // Display basic classification results
        this.displayBasicClassification(result.basic_classification);

        // Display LLM analysis if available
        if (result.llm_analysis) {
            this.displayLLMAnalysis(result.llm_analysis);
            document.getElementById('llm-analysis-section').classList.remove('d-none');
            document.getElementById('basic-recommendation-card').classList.add('d-none');
            
            // Show AI score section
            document.getElementById('llm-score-section').classList.remove('d-none');
        } else {
            document.getElementById('llm-analysis-section').classList.add('d-none');
            document.getElementById('basic-recommendation-card').classList.remove('d-none');
            this.displayBasicRecommendation(result.basic_classification);
            
            // Show message that LLM analysis is not available
            const aiScoreSection = document.getElementById('llm-score-section');
            const aiStatus = document.getElementById('ai-status');
            aiStatus.textContent = 'LLM analysis not available';
            aiStatus.className = 'text-muted';
            aiScoreSection.classList.remove('d-none');
            
            // Show info message
            this.showAlert('LLM analysis is not available. Please set up the GROQ_API_KEY environment variable for enhanced AI features.', 'info');
        }

        // Update result header
        this.updateResultHeader(result);
    }

    displayBasicClassification(classification) {
        const confidenceBar = document.getElementById('confidence-bar');
        const confidenceValue = document.getElementById('confidence-value');
        const publishableContent = document.getElementById('publishable-content');

        // Handle confidence score with proper validation
        let confidence = 0;
        if (classification.confidence !== null && classification.confidence !== undefined) {
            confidence = Math.round(parseFloat(classification.confidence) * 100);
            // Ensure confidence is within valid range
            confidence = Math.max(0, Math.min(100, confidence));
        }
        
        confidenceBar.style.width = `${confidence}%`;
        confidenceValue.textContent = `${confidence}%`;

        if (classification.publishable) {
            confidenceBar.className = 'progress-bar bg-success';
            publishableContent.innerHTML = `
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <strong>Publishable:</strong> This paper shows potential for publication.
                </div>
            `;
        } else {
            confidenceBar.className = 'progress-bar bg-danger';
            publishableContent.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-times-circle me-2"></i>
                    <strong>Not Publishable:</strong> This paper needs significant improvements.
                </div>
            `;
        }
    }

    displayLLMAnalysis(analysis) {
        // Update AI score
        const aiScoreValue = document.getElementById('ai-score-value');
        const aiScoreCircle = document.getElementById('ai-score-circle');
        const aiStatus = document.getElementById('ai-status');

        aiScoreValue.textContent = analysis.publishability_score.toFixed(1);
        aiScoreCircle.style.setProperty('--score', analysis.publishability_score);
        aiStatus.textContent = analysis.publishability_status.replace('_', ' ').toUpperCase();

        // Display strengths
        const strengthsList = document.getElementById('strengths-list');
        strengthsList.innerHTML = analysis.strengths.map(strength => 
            `<li class="mb-2"><i class="fas fa-check text-success me-2"></i>${strength}</li>`
        ).join('');

        // Display weaknesses
        const weaknessesList = document.getElementById('weaknesses-list');
        weaknessesList.innerHTML = analysis.weaknesses.map(weakness => 
            `<li class="mb-2"><i class="fas fa-exclamation-triangle text-warning me-2"></i>${weakness}</li>`
        ).join('');

        // Display detailed assessments
        document.getElementById('novelty-assessment').textContent = analysis.novelty_assessment;
        document.getElementById('technical-quality').textContent = analysis.technical_quality;
        document.getElementById('clarity-assessment').textContent = analysis.clarity_assessment;

        // Display improvement suggestions
        this.displayImprovementSuggestions(analysis.improvement_suggestions);

        // Display journal recommendations
        this.displayJournalRecommendations(analysis.journal_recommendations);

        // Display comparative analysis
        document.getElementById('comparative-analysis').textContent = analysis.comparative_analysis;
    }

    displayImprovementSuggestions(suggestions) {
        const container = document.getElementById('improvement-suggestions');
        
        if (suggestions.length === 0) {
            container.innerHTML = '<p class="text-muted">No specific suggestions available.</p>';
            return;
        }

        container.innerHTML = suggestions.map(suggestion => `
            <div class="suggestion-item ${suggestion.priority}-priority">
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <h6 class="mb-0">
                        <i class="fas fa-lightbulb me-2 text-warning"></i>
                        ${suggestion.category.replace('_', ' ').toUpperCase()}
                    </h6>
                    <span class="priority-badge priority-${suggestion.priority}">
                        ${suggestion.priority.toUpperCase()}
                    </span>
                </div>
                <p class="mb-2">${suggestion.description}</p>
                <div class="specific-actions">
                    <h6 class="small text-muted mb-1">Specific Actions:</h6>
                    <ul class="small mb-2">
                        ${suggestion.specific_actions.map(action => `<li>${action}</li>`).join('')}
                    </ul>
                </div>
                <div class="impact">
                    <small class="text-muted">
                        <i class="fas fa-chart-line me-1"></i>
                        <strong>Expected Impact:</strong> ${suggestion.impact}
                    </small>
                </div>
            </div>
        `).join('');
    }

    displayJournalRecommendations(recommendations) {
        const container = document.getElementById('journal-recommendations');
        
        if (recommendations.length === 0) {
            container.innerHTML = '<p class="text-muted">No journal recommendations available.</p>';
            return;
        }

        container.innerHTML = recommendations.map(rec => `
            <div class="journal-item">
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <div>
                        <h5 class="mb-1">${rec.journal_name}</h5>
                        <span class="badge bg-secondary">${rec.conference_type.toUpperCase()}</span>
                    </div>
                    <div class="text-end">
                        <div class="suitability-score">${rec.suitability_score.toFixed(1)}/10</div>
                        <div class="score-bar mt-1" style="width: 100px;">
                            <div class="score-fill" style="width: ${rec.suitability_score * 10}%"></div>
                        </div>
                    </div>
                </div>
                <div class="reasons mb-2">
                    <h6 class="small text-muted mb-1">Why this venue:</h6>
                    <ul class="small mb-0">
                        ${rec.reasons.map(reason => `<li>${reason}</li>`).join('')}
                    </ul>
                </div>
                <div class="requirements mb-2">
                    <h6 class="small text-muted mb-1">Requirements:</h6>
                    <ul class="small mb-0">
                        ${rec.requirements.map(req => `<li>${req}</li>`).join('')}
                    </ul>
                </div>
                <div class="submission-tips">
                    <h6 class="small text-muted mb-1">Submission Tips:</h6>
                    <ul class="small mb-0">
                        ${rec.submission_tips.map(tip => `<li>${tip}</li>`).join('')}
                    </ul>
                </div>
            </div>
        `).join('');
    }

    displayBasicRecommendation(classification) {
        document.getElementById('conference-name').textContent = classification.conference || 'N/A';
        document.getElementById('rationale-text').textContent = classification.rationale || 'No rationale available.';
    }

    updateResultHeader(result) {
        const iconContainer = document.getElementById('result-icon-container');
        const resultTitle = document.getElementById('result-title');
        const resultSummary = document.getElementById('result-summary');

        if (result.basic_classification.publishable) {
            iconContainer.innerHTML = '<i class="fas fa-check-circle fa-4x text-success"></i>';
            resultTitle.textContent = 'Paper Analysis Complete';
            resultSummary.textContent = 'Your paper shows potential for publication!';
        } else {
            iconContainer.innerHTML = '<i class="fas fa-exclamation-triangle fa-4x text-warning"></i>';
            resultTitle.textContent = 'Analysis Complete';
            resultSummary.textContent = 'Your paper needs improvements before publication.';
        }
    }

    async askAI() {
        const userQuery = document.getElementById('user-query').value.trim();
        if (!userQuery || !this.currentResult) return;

        const askButton = document.getElementById('ask-ai-btn');
        const originalText = askButton.innerHTML;
        askButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Asking AI...';
        askButton.disabled = true;

        try {
            const response = await fetch('/api/refine-analysis', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    paper_content: this.getPaperContent(),
                    previous_analysis: this.currentResult.llm_analysis,
                    user_query: userQuery
                })
            });

            const result = await response.json();

            if (response.ok) {
                document.getElementById('ai-response').classList.remove('d-none');
                document.getElementById('ai-response-text').textContent = result.refined_analysis;
                document.getElementById('user-query').value = '';
            } else {
                throw new Error(result.error || 'Failed to get AI response');
            }
        } catch (error) {
            this.showAlert(`AI query failed: ${error.message}`, 'danger');
        } finally {
            askButton.innerHTML = originalText;
            askButton.disabled = false;
        }
    }

    getPaperContent() {
        if (!this.currentResult || !this.currentResult.sections) {
            return '';
        }

        const sections = this.currentResult.sections;
        return `
Abstract: ${sections.abstract || ''}

Introduction: ${sections.introduction || ''}

Methodology: ${sections.methodology || ''}

Results: ${sections.results || ''}

Conclusion: ${sections.conclusion || ''}
        `.trim();
    }

    downloadReport() {
        if (!this.currentResult) return;

        const report = this.generateReport();
        const blob = new Blob([report], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${this.currentResult.paper_id}_analysis_report.txt`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    generateReport() {
        const result = this.currentResult;
        let report = `Research Paper Analysis Report\n`;
        report += `Paper ID: ${result.paper_id}\n`;
        report += `Generated: ${new Date().toLocaleString()}\n\n`;

        // Basic Classification
        report += `BASIC CLASSIFICATION\n`;
        report += `Publishable: ${result.basic_classification.publishable ? 'Yes' : 'No'}\n`;
        report += `Confidence: ${(result.basic_classification.confidence * 100).toFixed(1)}%\n`;
        report += `Recommended Venue: ${result.basic_classification.conference || 'N/A'}\n`;
        report += `Rationale: ${result.basic_classification.rationale || 'N/A'}\n\n`;

        // LLM Analysis
        if (result.llm_analysis) {
            const analysis = result.llm_analysis;
            report += `AI ANALYSIS\n`;
            report += `Publishability Score: ${analysis.publishability_score}/10\n`;
            report += `Status: ${analysis.publishability_status}\n\n`;

            report += `STRENGTHS:\n`;
            analysis.strengths.forEach(strength => {
                report += `- ${strength}\n`;
            });

            report += `\nWEAKNESSES:\n`;
            analysis.weaknesses.forEach(weakness => {
                report += `- ${weakness}\n`;
            });

            report += `\nDETAILED ASSESSMENTS:\n`;
            report += `Novelty: ${analysis.novelty_assessment}\n`;
            report += `Technical Quality: ${analysis.technical_quality}\n`;
            report += `Clarity: ${analysis.clarity_assessment}\n\n`;

            if (analysis.improvement_suggestions.length > 0) {
                report += `IMPROVEMENT SUGGESTIONS:\n`;
                analysis.improvement_suggestions.forEach((suggestion, index) => {
                    report += `${index + 1}. ${suggestion.category.toUpperCase()} (${suggestion.priority} priority)\n`;
                    report += `   ${suggestion.description}\n`;
                    report += `   Actions: ${suggestion.specific_actions.join(', ')}\n`;
                    report += `   Impact: ${suggestion.impact}\n\n`;
                });
            }

            if (analysis.journal_recommendations.length > 0) {
                report += `JOURNAL RECOMMENDATIONS:\n`;
                analysis.journal_recommendations.forEach((rec, index) => {
                    report += `${index + 1}. ${rec.journal_name} (${rec.suitability_score}/10)\n`;
                    report += `   Type: ${rec.conference_type}\n`;
                    report += `   Reasons: ${rec.reasons.join(', ')}\n`;
                    report += `   Requirements: ${rec.requirements.join(', ')}\n\n`;
                });
            }

            report += `COMPARATIVE ANALYSIS:\n${analysis.comparative_analysis}\n`;
        }

        return report;
    }

    resetForm() {
        this.currentSubmissionId = null;
        this.currentResult = null;
        this.selectedFile = null;

        document.getElementById('upload-section').classList.remove('d-none');
        document.getElementById('processing-section').classList.add('d-none');
        document.getElementById('results-section').classList.add('d-none');
        document.getElementById('selected-file').classList.add('d-none');
        document.getElementById('ai-response').classList.add('d-none');

        document.getElementById('file-input').value = '';
        document.getElementById('upload-btn').disabled = true;
        document.getElementById('user-query').value = '';

        // Reset progress
        document.getElementById('progress-bar').style.width = '0%';
        document.getElementById('progress-text').textContent = '0%';

        // Reset steps
        this.processingSteps.forEach(step => {
            const stepElement = document.getElementById(step.id);
            stepElement.classList.remove('active', 'completed');
        });
    }

    showAlert(message, type = 'info') {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        const container = document.querySelector('.container');
        container.insertBefore(alertDiv, container.firstChild);

        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }
}

// Initialize the enhanced analyzer when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new EnhancedPaperAnalyzer();
});

