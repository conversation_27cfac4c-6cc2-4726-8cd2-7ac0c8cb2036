# Vercel ignore file - exclude unnecessary files from deployment

# Python cache and compiled files
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
env/
ENV/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db

# Logs
*.log
logs/

# Test files and coverage
.pytest_cache/
.coverage
htmlcov/
.tox/

# Local development files
.env
.env.local

# Large data files (keep models but exclude large datasets)
data/raw/papers/*.pdf
results/*.png
results/*.json
results/*.csv

# Temporary files
tmp/
temp/

