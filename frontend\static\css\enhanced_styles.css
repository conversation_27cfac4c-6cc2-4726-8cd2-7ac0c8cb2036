/* Enhanced Styles for AI-Powered Research Paper Classification */

:root {
    --primary-color: #4361ee;
    --secondary-color: #3f37c9;
    --success-color: #06d6a0;
    --warning-color: #ffd60a;
    --danger-color: #f72585;
    --info-color: #4cc9f0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --gradient-primary: linear-gradient(135deg, #4361ee 0%, #3f37c9 100%);
    --gradient-success: linear-gradient(135deg, #06d6a0 0%, #118ab2 100%);
    --gradient-warning: linear-gradient(135deg, #ffd60a 0%, #f77f00 100%);
    --gradient-secondary: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

/* Global Styles */
body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
    line-height: 1.6;
    color: var(--dark-color);
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    min-height: 100vh;
}

/* Gradient Backgrounds */
.bg-gradient-primary {
    background: var(--gradient-primary) !important;
}

.bg-gradient-success {
    background: var(--gradient-success) !important;
}

.bg-gradient-warning {
    background: var(--gradient-warning) !important;
}

.bg-gradient-secondary {
    background: var(--gradient-secondary) !important;
}

/* Hero Section */
.hero-section {
    background: var(--gradient-primary);
    padding: 4rem 0;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.hero-section .container {
    position: relative;
    z-index: 1;
}

.feature-badges .badge {
    font-size: 0.9rem;
    padding: 0.5rem 1rem;
    border-radius: 50px;
    box-shadow: var(--shadow-sm);
}

/* Upload Section */
.upload-container {
    border: 3px dashed #dee2e6;
    border-radius: 1rem;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
}

.upload-container:hover {
    border-color: var(--primary-color);
    background: rgba(255, 255, 255, 0.95);
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.upload-container.dragover {
    border-color: var(--success-color);
    background: rgba(6, 214, 160, 0.1);
}

.upload-icon {
    transition: transform 0.3s ease;
}

.upload-container:hover .upload-icon {
    transform: scale(1.1);
}

/* Selected File Display */
.selected-file {
    animation: slideInUp 0.3s ease;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Processing Section */
.processing-animation {
    position: relative;
}

.processing-animation::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 80px;
    height: 80px;
    border: 2px solid rgba(67, 97, 238, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(1.5);
        opacity: 0;
    }
}

.processing-steps {
    margin-top: 2rem;
}

.step-item {
    padding: 1rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    opacity: 0.5;
}

.step-item.active {
    opacity: 1;
    background: rgba(67, 97, 238, 0.1);
    transform: scale(1.05);
}

.step-item.completed {
    opacity: 1;
    background: rgba(6, 214, 160, 0.1);
    color: var(--success-color);
}

/* Progress Bar */
.progress {
    border-radius: 50px;
    overflow: hidden;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.progress-bar.bg-gradient {
    background: var(--gradient-primary) !important;
}

#progress-text {
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Results Section */
.results-header {
    animation: fadeInUp 0.6s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Confidence Display */
.confidence-display .progress {
    height: 25px;
    border-radius: 50px;
}

/* AI Score Circle */
.score-circle {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: conic-gradient(var(--primary-color) 0deg, var(--primary-color) calc(var(--score, 0) * 36deg), #e9ecef calc(var(--score, 0) * 36deg));
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    margin: 0 auto;
    position: relative;
    box-shadow: var(--shadow);
}

.score-circle::before {
    content: '';
    position: absolute;
    width: 90px;
    height: 90px;
    background: white;
    border-radius: 50%;
    z-index: 1;
}

.score-circle span {
    position: relative;
    z-index: 2;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.score-circle small {
    position: relative;
    z-index: 2;
    color: var(--secondary-color);
    font-size: 0.8rem;
}

/* Assessment Items */
.assessment-item {
    padding: 1rem;
    border-radius: 0.5rem;
    background: rgba(255, 255, 255, 0.8);
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.assessment-item:hover {
    background: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-sm);
}

/* Improvement Suggestions */
.suggestion-item {
    border-left: 4px solid var(--primary-color);
    padding: 1rem;
    margin-bottom: 1rem;
    background: white;
    border-radius: 0 0.5rem 0.5rem 0;
    box-shadow: var(--shadow-sm);
    transition: all 0.3s ease;
}

.suggestion-item:hover {
    transform: translateX(5px);
    box-shadow: var(--shadow);
}

.suggestion-item.high-priority {
    border-left-color: var(--danger-color);
}

.suggestion-item.medium-priority {
    border-left-color: var(--warning-color);
}

.suggestion-item.low-priority {
    border-left-color: var(--info-color);
}

.priority-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 50px;
    font-weight: 600;
}

.priority-high {
    background: rgba(247, 37, 133, 0.1);
    color: var(--danger-color);
}

.priority-medium {
    background: rgba(255, 214, 10, 0.1);
    color: #f77f00;
}

.priority-low {
    background: rgba(76, 201, 240, 0.1);
    color: var(--info-color);
}

/* Journal Recommendations */
.journal-item {
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1rem;
    background: white;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.journal-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(to bottom, var(--success-color), var(--primary-color));
}

.journal-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.suitability-score {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary-color);
}

.score-bar {
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.score-fill {
    height: 100%;
    background: var(--gradient-success);
    border-radius: 4px;
    transition: width 0.6s ease;
}

/* Interactive Features */
.input-group .form-control {
    border-radius: 50px 0 0 50px;
    border: 2px solid #dee2e6;
    padding: 0.75rem 1.25rem;
}

.input-group .btn {
    border-radius: 0 50px 50px 0;
    padding: 0.75rem 1.5rem;
}

#ai-response {
    animation: slideInDown 0.3s ease;
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Cards */
.card {
    border-radius: 1rem;
    overflow: hidden;
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.card-header {
    border-bottom: none;
    padding: 1.25rem 1.5rem;
    font-weight: 600;
}

.card-body {
    padding: 1.5rem;
}

/* Buttons */
.btn {
    border-radius: 50px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    box-shadow: var(--shadow-sm);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.btn-primary {
    background: var(--gradient-primary);
}

.btn-lg {
    padding: 0.75rem 2rem;
    font-size: 1.1rem;
}

/* Utilities */
.text-white-50 {
    color: rgba(255, 255, 255, 0.5) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        padding: 2rem 0;
    }
    
    .display-4 {
        font-size: 2rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .btn-lg {
        padding: 0.5rem 1.5rem;
        font-size: 1rem;
    }
    
    .score-circle {
        width: 100px;
        height: 100px;
    }
    
    .score-circle::before {
        width: 75px;
        height: 75px;
    }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    :root {
        --light-color: #212529;
        --dark-color: #f8f9fa;
    }
    
    body {
        background: linear-gradient(135deg, #212529 0%, #343a40 100%);
        color: var(--dark-color);
    }
    
    .card {
        background: rgba(255, 255, 255, 0.05);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .upload-container {
        background: rgba(255, 255, 255, 0.05);
        border-color: rgba(255, 255, 255, 0.2);
    }
}

