/* Main Styles */
:root {
    --primary-color: #4361ee;
    --secondary-color: #3f37c9;
    --accent-color: #4895ef;
    --success-color: #4cc9f0;
    --warning-color: #f72585;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --border-radius: 0.5rem;
    --box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --transition: all 0.3s ease;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: var(--dark-color);
    background-color: #f5f7fa;
}

/* Navbar Styles */
.navbar {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
    font-weight: 600;
    letter-spacing: 0.5px;
}

/* Card Styles */
.card {
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    border: none;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 1rem 2rem rgba(0, 0, 0, 0.15);
}

/* Upload Container */
.upload-container {
    border: 2px dashed #dee2e6;
    border-radius: var(--border-radius);
    transition: var(--transition);
    background-color: #f8f9fa;
}

.upload-container:hover, .upload-container.dragover {
    border-color: var(--primary-color);
    background-color: rgba(67, 97, 238, 0.05);
}

/* Progress Bar */
.progress {
    border-radius: var(--border-radius);
    overflow: hidden;
}

.progress-bar {
    background-color: var(--primary-color);
}

/* Results Section */
#result-icon-container {
    width: 80px;
    height: 80px;
    margin: 0 auto;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
}

.publishable-icon {
    background-color: rgba(76, 201, 240, 0.2);
    color: var(--success-color);
}

.non-publishable-icon {
    background-color: rgba(247, 37, 133, 0.2);
    color: var(--warning-color);
}

/* Confidence Meter */
.confidence-meter .progress {
    height: 30px;
    border-radius: 15px;
}

.confidence-meter .progress-bar {
    border-radius: 15px;
}

/* Feature Cards */
.feature-card {
    transition: var(--transition);
    background-color: #fff;
}

.feature-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
}

/* Process Steps */
.process-content {
    background-color: #fff;
    transition: var(--transition);
}

.process-content:hover {
    transform: translateY(-3px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
}

.process-number .badge {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Feature Lists */
.feature-list {
    list-style: none;
    padding-left: 0;
}

.feature-list li {
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
}

/* Developer Badge */
.developer-badge {
    display: inline-block;
}

.developer-badge .badge {
    font-size: 0.9rem;
    padding: 0.5rem 1rem;
    border-radius: 50px;
}

/* Section Icons */
.section-icon {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(67, 97, 238, 0.1);
    border-radius: 50%;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .col-lg-3 .sticky-top {
        position: relative;
        top: 0 !important;
    }
}

/* Animation Effects */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.5s ease forwards;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-color);
}
