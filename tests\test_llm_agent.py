"""
Test cases for the LLM Agent functionality.

This module contains comprehensive tests for the Groq LLM agent integration,
including unit tests for individual components and integration tests for the
complete analysis workflow.
"""

import pytest
import os
import json
import sys
from unittest.mock import Mock, patch, MagicMock
from dataclasses import asdict

# Add src directory to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from llm_agent import (
    GroqLLMAgent, PaperAnalysis, ImprovementSuggestion, JournalRecommendation,
    analyze_paper_with_llm, get_improvement_suggestions, compare_journal_suitability
)

class TestGroqLLMAgent:
    """Test cases for the GroqLLMAgent class."""
    
    @pytest.fixture
    def mock_groq_client(self):
        """Mock Groq client for testing."""
        with patch('llm_agent.Groq') as mock_groq:
            mock_client = Mock()
            mock_groq.return_value = mock_client
            yield mock_client
    
    @pytest.fixture
    def sample_paper_content(self):
        """Sample paper content for testing."""
        return """
        Title: A Novel Approach to Machine Learning

        Abstract:
        This paper presents a novel approach to machine learning that combines
        traditional algorithms with modern deep learning techniques. Our method
        achieves state-of-the-art results on several benchmark datasets.

        Introduction:
        Machine learning has become increasingly important in recent years.
        This work addresses the limitations of existing approaches by proposing
        a hybrid methodology that leverages the strengths of both paradigms.

        Methodology:
        We propose a two-stage approach that first applies traditional feature
        extraction followed by deep neural network classification. The method
        is evaluated on standard benchmarks.

        Results:
        Our experiments show significant improvements over baseline methods,
        with accuracy improvements of up to 15% on challenging datasets.

        Conclusion:
        This work demonstrates the effectiveness of combining traditional and
        modern machine learning approaches. Future work will explore additional
        applications and optimizations.
        """
    
    @pytest.fixture
    def sample_llm_response(self):
        """Sample LLM response for testing."""
        return """
        {
            "publishability_score": 7.5,
            "publishability_status": "publishable",
            "publishability_reasons": [
                "Novel approach combining traditional and modern methods",
                "Strong experimental results with 15% improvement",
                "Clear methodology and evaluation"
            ],
            "strengths": [
                "Novel hybrid approach",
                "Strong experimental validation",
                "Clear writing and structure"
            ],
            "weaknesses": [
                "Limited theoretical analysis",
                "Could benefit from more extensive evaluation",
                "Related work section could be expanded"
            ],
            "novelty_assessment": "The paper presents a novel hybrid approach that combines traditional feature extraction with deep learning, which is a meaningful contribution to the field.",
            "technical_quality": "The technical approach is sound with proper experimental validation, though theoretical analysis could be strengthened.",
            "clarity_assessment": "The paper is well-written with clear structure and good presentation of results.",
            "improvement_suggestions": [
                {
                    "category": "methodology",
                    "priority": "high",
                    "description": "Add theoretical analysis of why the hybrid approach works",
                    "specific_actions": [
                        "Include mathematical formulation of the hybrid model",
                        "Provide theoretical justification for the two-stage approach"
                    ],
                    "impact": "Would strengthen the theoretical foundation and increase acceptance chances"
                }
            ],
            "journal_recommendations": [
                {
                    "journal_name": "Journal of Machine Learning Research",
                    "conference_type": "journal",
                    "suitability_score": 8.0,
                    "reasons": [
                        "Strong focus on novel ML methodologies",
                        "Appreciates hybrid approaches"
                    ],
                    "requirements": [
                        "Strong theoretical foundation",
                        "Comprehensive experimental evaluation"
                    ],
                    "typical_acceptance_criteria": "Novel contributions with solid experimental validation",
                    "submission_tips": [
                        "Emphasize the novelty of the hybrid approach",
                        "Include comprehensive comparison with existing methods"
                    ]
                }
            ],
            "comparative_analysis": "JMLR is the best fit due to its focus on methodological contributions and appreciation for hybrid approaches."
        }
        """
    
    def test_agent_initialization_with_api_key(self, mock_groq_client):
        """Test agent initialization with provided API key."""
        agent = GroqLLMAgent(api_key="test_key")
        assert agent.api_key == "test_key"
        assert agent.model == "llama-3.1-70b-versatile"
    
    def test_agent_initialization_from_env(self, mock_groq_client):
        """Test agent initialization from environment variable."""
        with patch.dict(os.environ, {'GROQ_API_KEY': 'env_test_key'}):
            agent = GroqLLMAgent()
            assert agent.api_key == "env_test_key"
    
    def test_agent_initialization_no_api_key(self, mock_groq_client):
        """Test agent initialization fails without API key."""
        with patch.dict(os.environ, {}, clear=True):
            with pytest.raises(ValueError, match="Groq API key is required"):
                GroqLLMAgent()
    
    def test_analyze_paper_success(self, mock_groq_client, sample_paper_content, sample_llm_response):
        """Test successful paper analysis."""
        # Setup mock response
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = sample_llm_response
        mock_groq_client.chat.completions.create.return_value = mock_response
        
        agent = GroqLLMAgent(api_key="test_key")
        result = agent.analyze_paper(sample_paper_content)
        
        assert isinstance(result, PaperAnalysis)
        assert result.publishability_score == 7.5
        assert result.publishability_status == "publishable"
        assert len(result.strengths) == 3
        assert len(result.weaknesses) == 3
        assert len(result.improvement_suggestions) == 1
        assert len(result.journal_recommendations) == 1
    
    def test_analyze_paper_api_failure(self, mock_groq_client, sample_paper_content):
        """Test paper analysis with API failure."""
        mock_groq_client.chat.completions.create.side_effect = Exception("API Error")
        
        agent = GroqLLMAgent(api_key="test_key")
        
        with pytest.raises(Exception, match="API Error"):
            agent.analyze_paper(sample_paper_content)
    
    def test_suggest_improvements(self, mock_groq_client, sample_paper_content):
        """Test improvement suggestions generation."""
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = """
        {
            "suggestions": [
                {
                    "category": "writing",
                    "priority": "medium",
                    "description": "Improve clarity of methodology section",
                    "specific_actions": [
                        "Add more detailed explanations",
                        "Include flowchart of the process"
                    ],
                    "impact": "Better understanding for reviewers"
                }
            ]
        }
        """
        mock_groq_client.chat.completions.create.return_value = mock_response
        
        agent = GroqLLMAgent(api_key="test_key")
        suggestions = agent.suggest_improvements(sample_paper_content)
        
        assert len(suggestions) == 1
        assert suggestions[0].category == "writing"
        assert suggestions[0].priority == "medium"
        assert len(suggestions[0].specific_actions) == 2
    
    def test_compare_journals(self, mock_groq_client, sample_paper_content):
        """Test journal comparison functionality."""
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = """
        {
            "recommendations": [
                {
                    "journal_name": "ICML",
                    "conference_type": "conference",
                    "suitability_score": 8.5,
                    "reasons": ["Strong ML focus", "Appreciates novel methods"],
                    "requirements": ["Novel contribution", "Strong experiments"],
                    "typical_acceptance_criteria": "High-quality ML research",
                    "submission_tips": ["Emphasize novelty", "Strong baselines"]
                }
            ]
        }
        """
        mock_groq_client.chat.completions.create.return_value = mock_response
        
        agent = GroqLLMAgent(api_key="test_key")
        recommendations = agent.compare_journals(sample_paper_content, ["ICML", "NeurIPS"])
        
        assert len(recommendations) == 1
        assert recommendations[0].journal_name == "ICML"
        assert recommendations[0].suitability_score == 8.5
        assert recommendations[0].conference_type == "conference"
    
    def test_refine_analysis(self, mock_groq_client, sample_paper_content):
        """Test analysis refinement functionality."""
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = "The methodology section could be improved by adding more mathematical details and theoretical justification."
        mock_groq_client.chat.completions.create.return_value = mock_response
        
        # Create a mock previous analysis
        previous_analysis = PaperAnalysis(
            publishability_score=7.0,
            publishability_status="publishable",
            publishability_reasons=["Good approach"],
            improvement_suggestions=[],
            journal_recommendations=[],
            comparative_analysis="",
            strengths=["Novel approach"],
            weaknesses=["Needs more theory"],
            novelty_assessment="Good",
            technical_quality="Adequate",
            clarity_assessment="Clear"
        )
        
        agent = GroqLLMAgent(api_key="test_key")
        refined = agent.refine_analysis(
            sample_paper_content, 
            previous_analysis, 
            "How can I improve the methodology section?"
        )
        
        assert "methodology section" in refined.lower()
        assert "mathematical details" in refined.lower()
    
    def test_parse_analysis_response_invalid_json(self, mock_groq_client):
        """Test parsing of invalid JSON response."""
        agent = GroqLLMAgent(api_key="test_key")
        
        # Test with invalid JSON
        result = agent._parse_analysis_response("Invalid JSON content")
        
        # Should return default analysis
        assert isinstance(result, PaperAnalysis)
        assert result.publishability_score == 5.0
        assert result.publishability_status == "needs_improvement"
        assert "Analysis parsing failed" in result.publishability_reasons
    
    def test_parse_analysis_response_missing_fields(self, mock_groq_client):
        """Test parsing of response with missing fields."""
        agent = GroqLLMAgent(api_key="test_key")
        
        # Test with minimal JSON
        minimal_response = '{"publishability_score": 6.0}'
        result = agent._parse_analysis_response(minimal_response)
        
        assert isinstance(result, PaperAnalysis)
        assert result.publishability_score == 6.0
        assert result.publishability_status == "needs_improvement"  # default value


class TestConvenienceFunctions:
    """Test cases for convenience functions."""
    
    @patch('llm_agent.GroqLLMAgent')
    def test_analyze_paper_with_llm_success(self, mock_agent_class):
        """Test successful paper analysis using convenience function."""
        mock_agent = Mock()
        mock_analysis = PaperAnalysis(
            publishability_score=8.0,
            publishability_status="publishable",
            publishability_reasons=["Strong contribution"],
            improvement_suggestions=[],
            journal_recommendations=[],
            comparative_analysis="Good fit for top venues",
            strengths=["Novel approach"],
            weaknesses=["Minor issues"],
            novelty_assessment="High novelty",
            technical_quality="Strong",
            clarity_assessment="Clear"
        )
        mock_agent.analyze_paper.return_value = mock_analysis
        mock_agent_class.return_value = mock_agent
        
        result = analyze_paper_with_llm("test content", {"title": "Test"}, "test_key")
        
        assert isinstance(result, dict)
        assert result["publishability_score"] == 8.0
        assert result["publishability_status"] == "publishable"
    
    @patch('llm_agent.GroqLLMAgent')
    def test_analyze_paper_with_llm_failure(self, mock_agent_class):
        """Test paper analysis failure using convenience function."""
        mock_agent_class.side_effect = Exception("API Error")
        
        result = analyze_paper_with_llm("test content", groq_api_key="test_key")
        
        assert "error" in result
        assert result["publishability_score"] == 0.0
        assert result["publishability_status"] == "error"
    
    @patch('llm_agent.GroqLLMAgent')
    def test_get_improvement_suggestions_success(self, mock_agent_class):
        """Test successful improvement suggestions using convenience function."""
        mock_agent = Mock()
        mock_suggestion = ImprovementSuggestion(
            category="methodology",
            priority="high",
            description="Improve experimental design",
            specific_actions=["Add control group", "Increase sample size"],
            impact="Higher confidence in results"
        )
        mock_agent.suggest_improvements.return_value = [mock_suggestion]
        mock_agent_class.return_value = mock_agent
        
        result = get_improvement_suggestions("test content", "test_key")
        
        assert len(result) == 1
        assert result[0]["category"] == "methodology"
        assert result[0]["priority"] == "high"
    
    @patch('llm_agent.GroqLLMAgent')
    def test_compare_journal_suitability_success(self, mock_agent_class):
        """Test successful journal comparison using convenience function."""
        mock_agent = Mock()
        mock_recommendation = JournalRecommendation(
            journal_name="ICML",
            conference_type="conference",
            suitability_score=8.5,
            reasons=["Strong ML focus"],
            requirements=["Novel contribution"],
            typical_acceptance_criteria="High quality research",
            submission_tips=["Emphasize novelty"]
        )
        mock_agent.compare_journals.return_value = [mock_recommendation]
        mock_agent_class.return_value = mock_agent
        
        result = compare_journal_suitability("test content", ["ICML"], "test_key")
        
        assert len(result) == 1
        assert result[0]["journal_name"] == "ICML"
        assert result[0]["suitability_score"] == 8.5


class TestDataClasses:
    """Test cases for data classes."""
    
    def test_improvement_suggestion_creation(self):
        """Test ImprovementSuggestion data class."""
        suggestion = ImprovementSuggestion(
            category="writing",
            priority="medium",
            description="Improve clarity",
            specific_actions=["Use simpler language", "Add examples"],
            impact="Better readability"
        )
        
        assert suggestion.category == "writing"
        assert suggestion.priority == "medium"
        assert len(suggestion.specific_actions) == 2
    
    def test_journal_recommendation_creation(self):
        """Test JournalRecommendation data class."""
        recommendation = JournalRecommendation(
            journal_name="Nature",
            conference_type="journal",
            suitability_score=9.0,
            reasons=["High impact", "Broad scope"],
            requirements=["Significant contribution", "Broad interest"],
            typical_acceptance_criteria="Groundbreaking research",
            submission_tips=["Emphasize impact", "Clear writing"]
        )
        
        assert recommendation.journal_name == "Nature"
        assert recommendation.suitability_score == 9.0
        assert len(recommendation.reasons) == 2
    
    def test_paper_analysis_creation(self):
        """Test PaperAnalysis data class."""
        analysis = PaperAnalysis(
            publishability_score=7.5,
            publishability_status="publishable",
            publishability_reasons=["Strong methodology"],
            improvement_suggestions=[],
            journal_recommendations=[],
            comparative_analysis="Good overall",
            strengths=["Novel approach"],
            weaknesses=["Limited evaluation"],
            novelty_assessment="High",
            technical_quality="Good",
            clarity_assessment="Clear"
        )
        
        assert analysis.publishability_score == 7.5
        assert analysis.publishability_status == "publishable"
        assert len(analysis.strengths) == 1


class TestPromptBuilding:
    """Test cases for prompt building functionality."""
    
    def test_build_analysis_prompt(self):
        """Test analysis prompt building."""
        agent = GroqLLMAgent(api_key="test_key")
        
        with patch.object(agent, '_call_groq_api'):
            prompt = agent._build_analysis_prompt("test content", {"title": "Test Paper"})
            
            assert "test content" in prompt
            assert "Test Paper" in prompt
            assert "publishability_score" in prompt
            assert "JSON format" in prompt
    
    def test_build_improvement_prompt(self):
        """Test improvement prompt building."""
        agent = GroqLLMAgent(api_key="test_key")
        
        with patch.object(agent, '_call_groq_api'):
            prompt = agent._build_improvement_prompt("test content")
            
            assert "test content" in prompt
            assert "improvement suggestions" in prompt
            assert "specific_actions" in prompt
    
    def test_build_comparison_prompt(self):
        """Test comparison prompt building."""
        agent = GroqLLMAgent(api_key="test_key")
        
        with patch.object(agent, '_call_groq_api'):
            prompt = agent._build_comparison_prompt("test content", ["ICML", "NeurIPS"])
            
            assert "test content" in prompt
            assert "ICML" in prompt
            assert "NeurIPS" in prompt
            assert "suitability_score" in prompt


if __name__ == "__main__":
    pytest.main([__file__, "-v"])

